/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ========== 事事了工单系统 - 自定义样式变量 ========== */
/* 主题色彩 */
/* 文字颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 状态颜色 */
/* 优先级颜色 */
/* 字体大小 */
/* 间距 */
/* 圆角 */
/* 阴影 */
/* 动画时间 */
/* 层级 */
.process-design-container.data-v-7074fd25 {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}
.header-section.data-v-7074fd25 {
  background: #ffffff;
  padding: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.process-info.data-v-7074fd25 {
  flex: 1;
}
.process-name.data-v-7074fd25 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}
.process-desc.data-v-7074fd25 {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
}
.save-btn.data-v-7074fd25 {
  background: #3498db;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
.save-btn.data-v-7074fd25:disabled {
  opacity: 0.6;
}
.nodes-section.data-v-7074fd25,
.preview-section.data-v-7074fd25 {
  background: #ffffff;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.section-title.data-v-7074fd25 {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  padding: 40rpx 40rpx 30rpx;
  border-bottom: 1rpx solid #f4f4f4;
}
.nodes-list.data-v-7074fd25 {
  padding: 40rpx;
}
.node-item.data-v-7074fd25 {
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}
.node-item.submit-node.data-v-7074fd25 {
  border-color: #3498db;
}
.node-item.approval-node.data-v-7074fd25 {
  border-color: #f39c12;
}
.node-header.data-v-7074fd25 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #ecf0f1;
  border-bottom: 1rpx solid #f4f4f4;
}
.node-icon.data-v-7074fd25 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #3498db;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 30rpx;
}
.node-info.data-v-7074fd25 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.node-title.data-v-7074fd25 {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4rpx;
}
.node-desc.data-v-7074fd25 {
  font-size: 24rpx;
  color: #7f8c8d;
}
.node-status.data-v-7074fd25 {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  background: #ecf0f1;
  color: #7f8c8d;
}
.node-actions.data-v-7074fd25 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.move-buttons.data-v-7074fd25 {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}
.move-btn.data-v-7074fd25 {
  width: 48rpx;
  height: 32rpx;
  border: 1rpx solid #e0e6ed;
  border-radius: 8rpx;
  background: #ffffff;
  color: #7f8c8d;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.move-btn.data-v-7074fd25:not(:disabled):active {
  background: #ecf0f1;
}
.move-btn.data-v-7074fd25:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}
.node-btn.data-v-7074fd25 {
  padding: 10rpx 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 20rpx;
}
.node-btn.edit-btn.data-v-7074fd25 {
  background: #3498db;
  color: #ffffff;
}
.node-btn.delete-btn.data-v-7074fd25 {
  background: #e74c3c;
  color: #ffffff;
}
.node-config.data-v-7074fd25 {
  padding: 30rpx;
}
.config-item.data-v-7074fd25 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.config-item.data-v-7074fd25:last-child {
  margin-bottom: 0;
}
.config-label.data-v-7074fd25 {
  font-size: 24rpx;
  color: #7f8c8d;
  min-width: 120rpx;
  flex-shrink: 0;
}
.config-value.data-v-7074fd25 {
  font-size: 24rpx;
  color: #2c3e50;
  flex: 1;
}
.department-tags.data-v-7074fd25 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
}
.department-tag.data-v-7074fd25 {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  background: #3498db;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 20rpx;
}
.tag-remove.data-v-7074fd25 {
  margin-left: 10rpx;
  font-size: 24rpx;
  cursor: pointer;
}
.add-department-btn.data-v-7074fd25 {
  padding: 4rpx 12rpx;
  border: 2rpx dashed #e0e6ed;
  border-radius: 8rpx;
  background: transparent;
  color: #7f8c8d;
  font-size: 20rpx;
}
.add-node-section.data-v-7074fd25 {
  text-align: center;
  padding: 40rpx;
}
.add-node-btn.data-v-7074fd25 {
  background: #3498db;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 30rpx 40rpx;
  font-size: 28rpx;
}
.flow-preview.data-v-7074fd25 {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.flow-step.data-v-7074fd25 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.step-icon.data-v-7074fd25 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #ffffff;
  margin-bottom: 20rpx;
}
.step-icon.submit.data-v-7074fd25 {
  background: #3498db;
}
.step-icon.approval.data-v-7074fd25 {
  background: #f39c12;
}
.step-icon.complete.data-v-7074fd25 {
  background: #2ecc71;
}
.step-text.data-v-7074fd25 {
  font-size: 24rpx;
  color: #2c3e50;
  text-align: center;
}
.step-arrow.data-v-7074fd25 {
  font-size: 32rpx;
  color: #7f8c8d;
  margin: 20rpx 0;
}
.modal-overlay.data-v-7074fd25 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-overlay.data-v-7074fd25:last-child {
  z-index: 1100;
}
.modal-content.data-v-7074fd25 {
  background: #ffffff;
  border-radius: 24rpx;
  width: 700rpx;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.modal-header.data-v-7074fd25 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f4f4f4;
}
.modal-title.data-v-7074fd25 {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}
.modal-close.data-v-7074fd25 {
  font-size: 40rpx;
  color: #7f8c8d;
  line-height: 1;
}
.modal-body.data-v-7074fd25 {
  flex: 1;
  padding: 40rpx;
  overflow-y: auto;
}
.form-item.data-v-7074fd25 {
  margin-bottom: 40rpx;
}
.form-label.data-v-7074fd25 {
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 20rpx;
  font-weight: 500;
}
.form-input.data-v-7074fd25 {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  background: #ffffff;
}
.form-input.data-v-7074fd25:focus {
  border-color: #3498db;
}
.radio-group.data-v-7074fd25,
.checkbox-group.data-v-7074fd25 {
  display: flex;
  gap: 40rpx;
}
.radio-item.data-v-7074fd25,
.checkbox-item.data-v-7074fd25 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #2c3e50;
}
.picker-input.data-v-7074fd25 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  background: #ffffff;
}
.picker-input .placeholder.data-v-7074fd25 {
  color: #bdc3c7;
}
.picker-input .picker-arrow.data-v-7074fd25 {
  color: #7f8c8d;
  font-size: 32rpx;
}
.approvers-input.data-v-7074fd25 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.approver-input.data-v-7074fd25 {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  background: #ffffff;
}
.add-approver-btn.data-v-7074fd25 {
  background: #3498db;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 24rpx;
}
.approvers-list.data-v-7074fd25 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.approver-tag.data-v-7074fd25 {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  background: #3498db;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 20rpx;
}
.department-list.data-v-7074fd25 {
  max-height: 400rpx;
  overflow-y: auto;
}
.department-item.data-v-7074fd25 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f4f4f4;
  font-size: 28rpx;
  color: #2c3e50;
}
.department-item.data-v-7074fd25:last-child {
  border-bottom: none;
}
.department-item.all-departments.data-v-7074fd25 {
  font-weight: 500;
  color: #3498db;
}
.department-divider.data-v-7074fd25 {
  height: 1rpx;
  background: #e0e6ed;
  margin: 20rpx 0;
}
.modal-footer.data-v-7074fd25 {
  display: flex;
  gap: 30rpx;
  padding: 40rpx;
  border-top: 1rpx solid #f4f4f4;
}
.modal-btn.data-v-7074fd25 {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
}
.modal-btn.cancel-btn.data-v-7074fd25 {
  background: #ecf0f1;
  color: #7f8c8d;
}
.modal-btn.confirm-btn.data-v-7074fd25 {
  background: #3498db;
  color: #ffffff;
}
.modal-btn.confirm-btn.data-v-7074fd25:disabled {
  opacity: 0.5;
}

/* 修复picker在modal中的层级问题 */
.modal-overlay.data-v-7074fd25 {
  z-index: 999 !important;
}
.modal-content.data-v-7074fd25 {
  z-index: 1000 !important;
  position: relative;
}
.modal-content picker.data-v-7074fd25 {
  z-index: 1002 !important;
  position: relative;
}
.modal-content .picker-input.data-v-7074fd25 {
  position: relative;
  z-index: 1002 !important;
}

/* 全局picker层级修复 */
.data-v-7074fd25 .uni-picker-container {
  z-index: 1003 !important;
}
.data-v-7074fd25 .uni-picker {
  z-index: 1003 !important;
}

/* 节点移动动画 */
.approval-node.data-v-7074fd25 {
  transition: all 0.3s ease;
}

/* 用户选择样式 */
.users-list.data-v-7074fd25 {
  max-height: 400rpx;
  overflow-y: auto;
}
.user-item.data-v-7074fd25 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #e0e6ed;
  cursor: pointer;
  transition: background-color 0.2s;
}
.user-item.data-v-7074fd25:hover {
  background: #ecf0f1;
}
.user-item.data-v-7074fd25:active {
  background: #f1f1f1;
}
.user-item checkbox.data-v-7074fd25 {
  margin-right: 30rpx;
}