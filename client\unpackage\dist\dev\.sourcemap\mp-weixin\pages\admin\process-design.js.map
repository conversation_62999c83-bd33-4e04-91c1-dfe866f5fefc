{"version": 3, "file": "process-design.js", "sources": ["pages/admin/process-design.vue", "D:/HBuilderX.3.4.18.20220630/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWRtaW4vcHJvY2Vzcy1kZXNpZ24udnVl"], "sourcesContent": ["<template>\n  <view class=\"process-design-container\">\n    <!-- 顶部信息 -->\n    <view class=\"header-section\">\n      <view class=\"process-info\">\n        <text class=\"process-name\">{{ processName }}</text>\n        <text class=\"process-desc\" v-if=\"processDescription\">{{ processDescription }}</text>\n        <text class=\"process-desc\" v-else>流程设计</text>\n      </view>\n      <button class=\"save-btn\" @click=\"saveProcess\" :disabled=\"saveLoading\">\n        <text v-if=\"saveLoading\">保存中...</text>\n        <text v-else>保存</text>\n      </button>\n    </view>\n\n    <!-- 节点列表 -->\n    <view class=\"nodes-section\">\n      <view class=\"section-title\">流程节点</view>\n      \n      <view class=\"nodes-list\">\n        <!-- 提交节点（固定） -->\n        <view class=\"node-item submit-node\">\n          <view class=\"node-header\">\n            <view class=\"node-icon\">\n              <Icon name=\"edit\" size=\"32\" color=\"#fff\" />\n            </view>\n            <view class=\"node-info\">\n              <text class=\"node-title\">提交节点</text>\n              <text class=\"node-desc\">工单提交入口</text>\n            </view>\n            <view class=\"node-status\">固定</view>\n          </view>\n          \n          <view class=\"node-config\">\n            <view class=\"config-item\">\n              <text class=\"config-label\">可提交部门：</text>\n              <view class=\"department-tags\">\n                <view \n                  v-for=\"dept in selectedSubmitDepartments\" \n                  :key=\"dept.id\"\n                  class=\"department-tag\"\n                >\n                  {{ dept.name }}\n                  <text class=\"tag-remove\" @click=\"removeSubmitDepartment(dept.id)\">×</text>\n                </view>\n                <button class=\"add-department-btn\" @click=\"showSubmitDepartmentPicker\">\n                  + 添加部门\n                </button>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 审批节点 -->\n        <view\n          v-for=\"(node, index) in approvalNodes\"\n          :key=\"index\"\n          class=\"node-item approval-node\"\n        >\n          <view class=\"node-header\">\n            <view class=\"node-icon\">\n              <Icon name=\"user\" size=\"32\" color=\"#fff\" />\n            </view>\n            <view class=\"node-info\">\n              <text class=\"node-title\">{{ node.name || `审批节点 ${index + 1}` }}</text>\n              <text class=\"node-desc\">{{ getNodeTypeText(node.type) }}</text>\n            </view>\n            <view class=\"node-actions\">\n              <view class=\"move-buttons\">\n                <button\n                  class=\"move-btn up-btn\"\n                  @click=\"moveNodeUp(index)\"\n                  :disabled=\"index === 0\"\n                >↑</button>\n                <button\n                  class=\"move-btn down-btn\"\n                  @click=\"moveNodeDown(index)\"\n                  :disabled=\"index === approvalNodes.length - 1\"\n                >↓</button>\n              </view>\n              <button class=\"node-btn edit-btn\" @click=\"editNode(index)\">编辑</button>\n              <button class=\"node-btn delete-btn\" @click=\"deleteNode(index)\">删除</button>\n            </view>\n          </view>\n          \n          <view class=\"node-config\">\n            <view class=\"config-item\">\n              <text class=\"config-label\">审批方式：</text>\n              <text class=\"config-value\">{{ getNodeTypeText(node.type) }}</text>\n            </view>\n            \n            <view class=\"config-item\" v-if=\"node.type === 'department'\">\n              <text class=\"config-label\">审批部门：</text>\n              <text class=\"config-value\">{{ node.departmentName }}</text>\n            </view>\n            \n            <view class=\"config-item\" v-if=\"node.type === 'person'\">\n              <text class=\"config-label\">审批人员：</text>\n              <text class=\"config-value\">{{ node.approvers.join(', ') }}</text>\n            </view>\n            \n            <view class=\"config-item\">\n              <text class=\"config-label\">允许转单：</text>\n              <text class=\"config-value\">{{ node.allowTransfer ? '是' : '否' }}</text>\n            </view>\n            \n            <view class=\"config-item\">\n              <text class=\"config-label\">允许驳回：</text>\n              <text class=\"config-value\">{{ node.allowReject ? '是' : '否' }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 添加节点按钮 -->\n        <view class=\"add-node-section\">\n          <button class=\"add-node-btn\" @click=\"showAddNodeModal\">\n            + 添加审批节点\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 流程预览 -->\n    <view class=\"preview-section\">\n      <view class=\"section-title\">流程预览</view>\n      <view class=\"flow-preview\">\n        <view class=\"flow-step\">\n          <view class=\"step-icon submit\">\n            <Icon name=\"edit\" size=\"28\" color=\"#fff\" />\n          </view>\n          <text class=\"step-text\">提交工单</text>\n        </view>\n        \n        <view v-for=\"(node, index) in approvalNodes\" :key=\"index\" class=\"flow-step\">\n          <view class=\"step-arrow\">↓</view>\n          <view class=\"step-icon approval\">\n            <Icon name=\"user\" size=\"28\" color=\"#fff\" />\n          </view>\n          <text class=\"step-text\">{{ node.name || `审批节点${index + 1}` }}</text>\n        </view>\n        \n        <view class=\"flow-step\">\n          <view class=\"step-arrow\">↓</view>\n          <view class=\"step-icon complete\">\n            <Icon name=\"check\" size=\"28\" color=\"#fff\" />\n          </view>\n          <text class=\"step-text\">完成</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 添加/编辑节点弹窗 -->\n    <view class=\"modal-overlay\" v-if=\"showNodeModal\" @click=\"hideNodeModal\">\n      <view class=\"modal-content\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">{{ isEditNode ? '编辑节点' : '添加节点' }}</text>\n          <text class=\"modal-close\" @click=\"hideNodeModal\">×</text>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"form-item\">\n            <view class=\"form-label\">节点名称</view>\n            <input \n              class=\"form-input\" \n              type=\"text\" \n              placeholder=\"请输入节点名称\"\n              v-model=\"nodeForm.name\"\n              maxlength=\"20\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"form-label\">审批类型</view>\n            <view class=\"radio-group\">\n              <label class=\"radio-item\" @click=\"selectNodeType('department')\">\n                <radio value=\"department\" :checked=\"nodeForm.type === 'department'\" />\n                <text>部门审批</text>\n              </label>\n              <label class=\"radio-item\" @click=\"selectNodeType('person')\">\n                <radio value=\"person\" :checked=\"nodeForm.type === 'person'\" />\n                <text>个人审批</text>\n              </label>\n            </view>\n          </view>\n          \n          <view class=\"form-item\" v-if=\"nodeForm.type === 'department'\">\n            <view class=\"form-label\">选择部门</view>\n            <picker \n              mode=\"selector\" \n              :range=\"departmentList\" \n              range-key=\"name\"\n              @change=\"onDepartmentChange\"\n              :value=\"selectedDepartmentIndex\"\n            >\n              <view class=\"picker-input\">\n                <text v-if=\"nodeForm.departmentId\">{{ selectedDepartmentName }}</text>\n                <text v-else class=\"placeholder\">请选择部门</text>\n                <text class=\"picker-arrow\">></text>\n              </view>\n            </picker>\n          </view>\n          \n          <view class=\"form-item\" v-if=\"nodeForm.type === 'person'\">\n            <view class=\"form-label\">选择审批人员</view>\n            <view class=\"picker-input\" @click=\"showUserModal = true\">\n              <text v-if=\"nodeForm.approvers.length > 0\">已选择 {{ nodeForm.approvers.length }} 人</text>\n              <text v-else class=\"placeholder\">请选择审批人员</text>\n              <text class=\"picker-arrow\">></text>\n            </view>\n            <view class=\"selected-users\" v-if=\"nodeForm.approvers.length > 0\">\n              <text class=\"selected-label\">已选择：</text>\n              <view\n                v-for=\"(userId, index) in nodeForm.approvers\"\n                :key=\"index\"\n                class=\"user-tag\"\n              >\n                {{ getUserDisplayName(userId) }}\n                <text class=\"tag-remove\" @click=\"removeUser(index)\">×</text>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"form-label\">节点设置</view>\n            <view class=\"checkbox-group\">\n              <label class=\"checkbox-item\">\n                <checkbox :checked=\"nodeForm.allowTransfer\" @change=\"onAllowTransferChange\" />\n                <text>允许转单</text>\n              </label>\n              <label class=\"checkbox-item\">\n                <checkbox :checked=\"nodeForm.allowReject\" @change=\"onAllowRejectChange\" />\n                <text>允许驳回</text>\n              </label>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <button class=\"modal-btn cancel-btn\" @click=\"hideNodeModal\">取消</button>\n          <button \n            class=\"modal-btn confirm-btn\" \n            @click=\"saveNode\"\n            :disabled=\"!validateNodeForm()\"\n          >\n            {{ isEditNode ? '保存' : '添加' }}\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 部门选择弹窗 -->\n    <view class=\"modal-overlay\" v-if=\"showDepartmentModal\" @click=\"hideDepartmentModal\">\n      <view class=\"modal-content\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择可提交部门</text>\n          <text class=\"modal-close\" @click=\"hideDepartmentModal\">×</text>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"department-list\">\n            <!-- 全部部门选项 -->\n            <label class=\"department-item all-departments\">\n              <checkbox\n                value=\"all\"\n                :checked=\"isAllDepartmentsSelected\"\n                @change=\"onAllDepartmentsChange\"\n              />\n              <text>全部部门</text>\n            </label>\n\n            <!-- 分隔线 -->\n            <view class=\"department-divider\"></view>\n\n            <!-- 具体部门列表 -->\n            <label\n              v-for=\"dept in departmentList\"\n              :key=\"dept.id\"\n              class=\"department-item\"\n              @click=\"toggleSubmitDepartment(dept)\"\n            >\n              <checkbox\n                :value=\"String(dept.id)\"\n                :checked=\"isSubmitDepartmentSelected(dept.id)\"\n              />\n              <text>{{ dept.name }}</text>\n            </label>\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <button class=\"modal-btn confirm-btn\" @click=\"hideDepartmentModal\">确定</button>\n        </view>\n      </view>\n    </view>\n  </view>\n\n  <!-- 用户选择模态框 -->\n  <view v-if=\"showUserModal\" class=\"modal-overlay\" @click=\"hideUserModal\">\n    <view class=\"modal-content\" @click.stop>\n      <view class=\"modal-header\">\n        <text class=\"modal-title\">选择审批人员</text>\n        <text class=\"modal-close\" @click=\"hideUserModal\">×</text>\n      </view>\n\n      <view class=\"modal-body\">\n        <view class=\"users-list\">\n          <label\n            v-for=\"user in userList\"\n            :key=\"user.id\"\n            class=\"user-item\"\n            @click=\"toggleUser(user)\"\n          >\n            <checkbox\n              :value=\"String(user.id)\"\n              :checked=\"isUserSelected(user.id)\"\n            />\n            <text>{{ user.departmentName }}-{{ user.name }}</text>\n          </label>\n        </view>\n      </view>\n\n      <view class=\"modal-footer\">\n        <button class=\"modal-btn confirm-btn\" @click=\"hideUserModal\">确定</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      processId: '',\n      processName: '',\n      processDescription: '',\n      saveLoading: false,\n      \n      // 节点数据\n      selectedSubmitDepartments: [], // 可提交部门\n      approvalNodes: [], // 审批节点\n      \n      // 弹窗状态\n      showNodeModal: false,\n      showDepartmentModal: false,\n      showUserModal: false,\n      isEditNode: false,\n      editNodeIndex: -1,\n      \n      // 表单数据\n      nodeForm: {\n        name: '',\n        type: 'department', // department | person\n        departmentId: '',\n        departmentName: '',\n        approvers: [],\n        allowTransfer: true,\n        allowReject: true\n      },\n      \n      currentApprover: '',\n      selectedDepartmentIndex: -1,\n      \n      // 基础数据\n      departmentList: [],\n      userList: []\n    }\n  },\n  \n  computed: {\n    selectedDepartmentName() {\n      if (this.selectedDepartmentIndex >= 0) {\n        return this.departmentList[this.selectedDepartmentIndex].name\n      }\n      return ''\n    },\n\n    // 是否选择了全部部门\n    isAllDepartmentsSelected() {\n      return this.departmentList.length > 0 &&\n             this.selectedSubmitDepartments.length === this.departmentList.length\n    }\n  },\n  \n  async onLoad(options) {\n    this.processId = options.id\n    this.processName = decodeURIComponent(options.name || '未命名流程')\n\n    // 先加载基础数据，再加载流程详情\n    await this.loadDepartments()\n    await this.loadUsers()\n    await this.loadProcessDetail()\n  },\n  \n  methods: {\n    // 加载部门列表\n    async loadDepartments() {\n      try {\n        const result = await this.$api.department.list()\n        console.log('部门API返回:', result)\n        if (result.code === 200) {\n          // 检查数据结构\n          if (result.data && result.data.list) {\n            this.departmentList = result.data.list\n          } else {\n            this.departmentList = result.data || []\n          }\n          console.log('部门列表:', this.departmentList)\n        }\n      } catch (error) {\n        console.error('加载部门列表失败:', error)\n      }\n    },\n\n    // 加载用户列表\n    async loadUsers() {\n      try {\n        const result = await this.$api.user.getList()\n        console.log('用户API返回:', result)\n        if (result.code === 200) {\n          // 检查数据结构\n          if (result.data && result.data.list) {\n            this.userList = result.data.list\n          } else {\n            this.userList = result.data || []\n          }\n          console.log('用户列表:', this.userList)\n        }\n      } catch (error) {\n        console.error('加载用户列表失败:', error)\n      }\n    },\n\n    // 加载流程详情\n    async loadProcessDetail() {\n      try {\n        const result = await this.$api.process.getDetail(this.processId)\n        console.log('流程详情API返回:', result)\n        if (result.code === 200) {\n          const process = result.data\n          const nodes = process.nodes || []\n\n          // 设置流程描述\n          this.processDescription = process.description || ''\n\n          // 如果有节点，从第一个节点（提交节点）获取部门信息\n          if (nodes.length > 0 && nodes[0].type === 'submit') {\n            const submitNode = nodes[0]\n            // 根据部门ID数组找到对应的部门对象\n            const departmentIds = submitNode.departments || []\n            this.selectedSubmitDepartments = this.departmentList.filter(dept =>\n              departmentIds.includes(dept.id)\n            )\n            // 审批节点从第二个开始\n            this.approvalNodes = nodes.slice(1)\n          } else {\n            // 如果没有节点，初始化为空\n            this.selectedSubmitDepartments = []\n            this.approvalNodes = []\n          }\n\n          console.log('加载的提交部门:', this.selectedSubmitDepartments)\n          console.log('加载的审批节点:', this.approvalNodes)\n        }\n      } catch (error) {\n        console.error('加载流程详情失败:', error)\n      }\n    },\n    \n    // 获取节点类型文本\n    getNodeTypeText(type) {\n      const typeMap = {\n        'department': '部门审批',\n        'person': '个人审批'\n      }\n      return typeMap[type] || '未知类型'\n    },\n    \n    // 显示添加节点弹窗\n    showAddNodeModal() {\n      this.isEditNode = false\n      this.editNodeIndex = -1\n      this.nodeForm = {\n        name: '',\n        type: 'department',\n        departmentId: '',\n        departmentName: '',\n        approvers: [],\n        allowTransfer: true,\n        allowReject: true\n      }\n      this.currentApprover = ''\n      this.selectedDepartmentIndex = -1\n      this.showNodeModal = true\n    },\n    \n    // 编辑节点\n    editNode(index) {\n      this.isEditNode = true\n      this.editNodeIndex = index\n      const node = this.approvalNodes[index]\n      \n      this.nodeForm = {\n        name: node.name || '',\n        type: node.type,\n        departmentId: node.departmentId || '',\n        departmentName: node.departmentName || '',\n        approvers: [...(node.approvers || [])],\n        allowTransfer: node.allowTransfer !== false,\n        allowReject: node.allowReject !== false\n      }\n      \n      // 设置部门选择器索引\n      if (node.departmentId) {\n        this.selectedDepartmentIndex = this.departmentList.findIndex(\n          dept => dept.id === node.departmentId\n        )\n      } else {\n        this.selectedDepartmentIndex = -1\n      }\n      \n      this.currentApprover = ''\n      this.showNodeModal = true\n    },\n    \n    // 删除节点\n    deleteNode(index) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这个审批节点吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.approvalNodes.splice(index, 1)\n          }\n        }\n      })\n    },\n    \n    // 隐藏节点弹窗\n    hideNodeModal() {\n      this.showNodeModal = false\n    },\n    \n    // 选择节点类型（单选，不能取消）\n    selectNodeType(type) {\n      this.nodeForm.type = type\n\n      // 清空相关字段\n      this.nodeForm.departmentId = ''\n      this.nodeForm.departmentName = ''\n      this.nodeForm.approvers = []\n      this.selectedDepartmentIndex = -1\n    },\n\n    // 节点类型变化（保留原方法以防其他地方使用）\n    onNodeTypeChange(e) {\n      this.nodeForm.type = e.detail.value\n      // 清空相关字段\n      this.nodeForm.departmentId = ''\n      this.nodeForm.departmentName = ''\n      this.nodeForm.approvers = []\n      this.selectedDepartmentIndex = -1\n    },\n    \n    // 部门选择变化\n    onDepartmentChange(e) {\n      this.selectedDepartmentIndex = e.detail.value\n      const dept = this.departmentList[e.detail.value]\n      this.nodeForm.departmentId = dept.id\n      this.nodeForm.departmentName = dept.name\n    },\n    \n    // 添加审批人\n    addApprover() {\n      const approver = this.currentApprover.trim()\n      if (!approver) {\n        uni.showToast({\n          title: '请输入工号',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (this.nodeForm.approvers.includes(approver)) {\n        uni.showToast({\n          title: '工号已存在',\n          icon: 'none'\n        })\n        return\n      }\n      \n      this.nodeForm.approvers.push(approver)\n      this.currentApprover = ''\n    },\n    \n    // 检查用户是否已选择\n    isUserSelected(userId) {\n      return this.nodeForm.approvers.includes(userId)\n    },\n\n    // 用户选择变化\n    onUserChange(e) {\n      const userId = parseInt(e.detail.value)\n\n      if (e.detail.checked) {\n        if (!this.isUserSelected(userId)) {\n          this.nodeForm.approvers.push(userId)\n        }\n      } else {\n        this.nodeForm.approvers = this.nodeForm.approvers.filter(id => id !== userId)\n      }\n    },\n\n    // 获取用户显示名称\n    getUserDisplayName(userId) {\n      const user = this.userList.find(u => u.id === userId)\n      return user ? `${user.departmentName}-${user.name}` : `用户${userId}`\n    },\n\n    // 移除用户\n    removeUser(index) {\n      this.nodeForm.approvers.splice(index, 1)\n    },\n\n    // 移除审批人（保留兼容性）\n    removeApprover(index) {\n      this.nodeForm.approvers.splice(index, 1)\n    },\n    \n    // 允许转单变化\n    onAllowTransferChange(e) {\n      this.nodeForm.allowTransfer = e.detail.value.length > 0\n    },\n    \n    // 允许驳回变化\n    onAllowRejectChange(e) {\n      this.nodeForm.allowReject = e.detail.value.length > 0\n    },\n    \n    // 验证节点表单\n    validateNodeForm() {\n      if (!this.nodeForm.name.trim()) {\n        return false\n      }\n      \n      if (this.nodeForm.type === 'department' && !this.nodeForm.departmentId) {\n        return false\n      }\n      \n      if (this.nodeForm.type === 'person' && this.nodeForm.approvers.length === 0) {\n        return false\n      }\n      \n      return true\n    },\n    \n    // 保存节点\n    saveNode() {\n      if (!this.validateNodeForm()) {\n        uni.showToast({\n          title: '请完善节点信息',\n          icon: 'none'\n        })\n        return\n      }\n      \n      const nodeData = { ...this.nodeForm }\n      \n      if (this.isEditNode) {\n        this.approvalNodes.splice(this.editNodeIndex, 1, nodeData)\n      } else {\n        this.approvalNodes.push(nodeData)\n      }\n      \n      this.hideNodeModal()\n    },\n    \n    // 显示提交部门选择弹窗\n    showSubmitDepartmentPicker() {\n      this.showDepartmentModal = true\n    },\n    \n    // 隐藏部门选择弹窗\n    hideDepartmentModal() {\n      this.showDepartmentModal = false\n    },\n\n    // 显示用户选择弹窗\n    showUserPicker() {\n      this.showUserModal = true\n    },\n\n    // 隐藏用户选择弹窗\n    hideUserModal() {\n      this.showUserModal = false\n    },\n\n    // 切换用户选择\n    toggleUser(user) {\n      const userId = user.id\n      if (this.isUserSelected(userId)) {\n        // 如果已选择，则移除\n        this.nodeForm.approvers = this.nodeForm.approvers.filter(u => u.id !== userId)\n      } else {\n        // 如果未选择，则添加\n        this.nodeForm.approvers.push({\n          id: user.id,\n          name: user.name,\n          departmentName: user.departmentName\n        })\n      }\n    },\n    \n    // 检查提交部门是否已选择\n    isSubmitDepartmentSelected(deptId) {\n      return this.selectedSubmitDepartments.some(dept => dept.id === deptId)\n    },\n    \n    // 全部部门选择变化\n    onAllDepartmentsChange(e) {\n      if (e.detail.checked) {\n        // 选择全部部门\n        this.selectedSubmitDepartments = [...this.departmentList]\n      } else {\n        // 取消全部部门\n        this.selectedSubmitDepartments = []\n      }\n    },\n\n    // 切换提交部门选择\n    toggleSubmitDepartment(dept) {\n      console.log('切换部门选择:', dept)\n\n      if (this.isSubmitDepartmentSelected(dept.id)) {\n        // 如果已选择，则移除\n        this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(\n          d => d.id !== dept.id\n        )\n      } else {\n        // 如果未选择，则添加\n        this.selectedSubmitDepartments.push(dept)\n      }\n\n      console.log('部门选择变化后:', this.selectedSubmitDepartments)\n    },\n\n    // 提交部门选择变化（保留兼容性）\n    onSubmitDepartmentChange(e) {\n      const deptId = parseInt(e.detail.value) // 转换为数字\n      const dept = this.departmentList.find(d => d.id === deptId)\n\n      console.log('部门选择变化:', {\n        deptId,\n        dept,\n        checked: e.detail.checked,\n        currentSelected: this.selectedSubmitDepartments\n      })\n\n      if (e.detail.checked) {\n        if (!this.isSubmitDepartmentSelected(deptId)) {\n          this.selectedSubmitDepartments.push(dept)\n        }\n      } else {\n        this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(\n          d => d.id !== deptId\n        )\n      }\n\n      console.log('部门选择变化后:', this.selectedSubmitDepartments)\n    },\n    \n    // 移除提交部门\n    removeSubmitDepartment(deptId) {\n      this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(\n        dept => dept.id !== deptId\n      )\n    },\n    \n    // 保存流程\n    async saveProcess() {\n      console.log('保存流程 - 选中的部门:', this.selectedSubmitDepartments)\n      console.log('保存流程 - 部门数量:', this.selectedSubmitDepartments.length)\n\n      if (this.selectedSubmitDepartments.length === 0) {\n        uni.showToast({\n          title: '请至少选择一个可提交部门',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (this.approvalNodes.length === 0) {\n        uni.showToast({\n          title: '请至少添加一个审批节点',\n          icon: 'none'\n        })\n        return\n      }\n      \n      this.saveLoading = true\n      \n      try {\n        // 格式化节点数据\n        const formattedNodes = this.approvalNodes.map(node => {\n          const formattedNode = {\n            type: 'approve', // 统一设置为审批节点\n            name: node.name || '审批节点'\n          }\n\n          // 根据审批类型设置相应字段\n          if (node.type === 'department') {\n            formattedNode.approvalType = 'department'\n            formattedNode.departments = [{\n              id: node.departmentId,\n              name: node.departmentName\n            }]\n          } else if (node.type === 'user') {\n            formattedNode.approvalType = 'user'\n            formattedNode.users = node.approvers || []\n          }\n\n          return formattedNode\n        })\n\n        const processData = {\n          submitDepartments: this.selectedSubmitDepartments.map(dept => dept.id),\n          nodes: formattedNodes\n        }\n\n        console.log('发送的流程数据:', processData)\n        \n        const result = await this.$api.process.updateDesign(this.processId, processData)\n        \n        if (result.code === 200) {\n          uni.showToast({\n            title: '保存成功',\n            icon: 'success'\n          })\n          \n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        } else {\n          uni.showToast({\n            title: result.message || '保存失败',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('保存流程失败:', error)\n        uni.showToast({\n          title: '保存失败，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.saveLoading = false\n      }\n    },\n\n    // 上移节点\n    moveNodeUp(index) {\n      if (index > 0) {\n        // 使用数组splice方法确保响应式更新\n        const nodeToMove = this.approvalNodes.splice(index, 1)[0]\n        this.approvalNodes.splice(index - 1, 0, nodeToMove)\n\n        // 添加移动提示\n        uni.showToast({\n          title: '节点已上移',\n          icon: 'success',\n          duration: 1000\n        })\n      }\n    },\n\n    // 下移节点\n    moveNodeDown(index) {\n      if (index < this.approvalNodes.length - 1) {\n        // 使用数组splice方法确保响应式更新\n        const nodeToMove = this.approvalNodes.splice(index, 1)[0]\n        this.approvalNodes.splice(index + 1, 0, nodeToMove)\n\n        // 添加移动提示\n        uni.showToast({\n          title: '节点已下移',\n          icon: 'success',\n          duration: 1000\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.process-design-container {\n  min-height: 100vh;\n  background: $bg-color-page;\n  padding-bottom: 120rpx;\n}\n\n.header-section {\n  background: $bg-color-white;\n  padding: $spacing-lg;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $spacing-base;\n  box-shadow: $box-shadow-light;\n}\n\n.process-info {\n  flex: 1;\n}\n\n.process-name {\n  display: block;\n  font-size: $font-size-xl;\n  font-weight: bold;\n  color: $text-color-primary;\n  margin-bottom: $spacing-xs;\n}\n\n.process-desc {\n  display: block;\n  font-size: $font-size-base;\n  color: $text-color-secondary;\n}\n\n.save-btn {\n  background: $primary-color;\n  color: $text-color-white;\n  border: none;\n  border-radius: $border-radius-base;\n  padding: $spacing-sm $spacing-lg;\n  font-size: $font-size-base;\n  \n  &:disabled {\n    opacity: 0.6;\n  }\n}\n\n.nodes-section,\n.preview-section {\n  background: $bg-color-white;\n  margin-bottom: $spacing-base;\n  box-shadow: $box-shadow-light;\n}\n\n.section-title {\n  font-size: $font-size-lg;\n  font-weight: bold;\n  color: $text-color-primary;\n  padding: $spacing-lg $spacing-lg $spacing-base;\n  border-bottom: 1rpx solid $border-color-light;\n}\n\n.nodes-list {\n  padding: $spacing-lg;\n}\n\n.node-item {\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  margin-bottom: $spacing-base;\n  overflow: hidden;\n  \n  &.submit-node {\n    border-color: $primary-color;\n  }\n  \n  &.approval-node {\n    border-color: $warning-color;\n  }\n}\n\n.node-header {\n  display: flex;\n  align-items: center;\n  padding: $spacing-base;\n  background: $bg-color-light;\n  border-bottom: 1rpx solid $border-color-light;\n}\n\n.node-icon {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  background: $primary-color;\n  color: $text-color-white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: $font-size-lg;\n  margin-right: $spacing-base;\n}\n\n.node-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.node-title {\n  font-size: $font-size-base;\n  font-weight: bold;\n  color: $text-color-primary;\n  margin-bottom: 4rpx;\n}\n\n.node-desc {\n  font-size: $font-size-sm;\n  color: $text-color-secondary;\n}\n\n.node-status {\n  padding: 4rpx 12rpx;\n  border-radius: 8rpx;\n  font-size: $font-size-xs;\n  background: $bg-color-light;\n  color: $text-color-secondary;\n}\n\n.node-actions {\n  display: flex;\n  align-items: center;\n  gap: $spacing-sm;\n}\n\n.move-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: 4rpx;\n}\n\n.move-btn {\n  width: 48rpx;\n  height: 32rpx;\n  border: 1rpx solid $border-color;\n  border-radius: $border-radius-sm;\n  background: $bg-color-white;\n  color: $text-color-secondary;\n  font-size: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:not(:disabled):active {\n    background: $bg-color-light;\n  }\n\n  &:disabled {\n    opacity: 0.3;\n    cursor: not-allowed;\n  }\n}\n\n.node-btn {\n  padding: $spacing-xs $spacing-sm;\n  border: none;\n  border-radius: $border-radius-sm;\n  font-size: $font-size-xs;\n  \n  &.edit-btn {\n    background: $primary-color;\n    color: $text-color-white;\n  }\n  \n  &.delete-btn {\n    background: $danger-color;\n    color: $text-color-white;\n  }\n}\n\n.node-config {\n  padding: $spacing-base;\n}\n\n.config-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: $spacing-sm;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.config-label {\n  font-size: $font-size-sm;\n  color: $text-color-secondary;\n  min-width: 120rpx;\n  flex-shrink: 0;\n}\n\n.config-value {\n  font-size: $font-size-sm;\n  color: $text-color-primary;\n  flex: 1;\n}\n\n.department-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: $spacing-sm;\n  align-items: center;\n}\n\n.department-tag {\n  display: flex;\n  align-items: center;\n  padding: 4rpx 12rpx;\n  background: $primary-color;\n  color: $text-color-white;\n  border-radius: 8rpx;\n  font-size: $font-size-xs;\n}\n\n.tag-remove {\n  margin-left: $spacing-xs;\n  font-size: $font-size-sm;\n  cursor: pointer;\n}\n\n.add-department-btn {\n  padding: 4rpx 12rpx;\n  border: 2rpx dashed $border-color;\n  border-radius: 8rpx;\n  background: transparent;\n  color: $text-color-secondary;\n  font-size: $font-size-xs;\n}\n\n.add-node-section {\n  text-align: center;\n  padding: $spacing-lg;\n}\n\n.add-node-btn {\n  background: $primary-color;\n  color: $text-color-white;\n  border: none;\n  border-radius: $border-radius-base;\n  padding: $spacing-base $spacing-lg;\n  font-size: $font-size-base;\n}\n\n.flow-preview {\n  padding: $spacing-lg;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.flow-step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: $spacing-base;\n}\n\n.step-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: $font-size-lg;\n  color: $text-color-white;\n  margin-bottom: $spacing-sm;\n  \n  &.submit {\n    background: $primary-color;\n  }\n  \n  &.approval {\n    background: $warning-color;\n  }\n  \n  &.complete {\n    background: $success-color;\n  }\n}\n\n.step-text {\n  font-size: $font-size-sm;\n  color: $text-color-primary;\n  text-align: center;\n}\n\n.step-arrow {\n  font-size: $font-size-lg;\n  color: $text-color-secondary;\n  margin: $spacing-sm 0;\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n\n  // 部门选择弹窗需要更高的层级\n  &:last-child {\n    z-index: 1100;\n  }\n}\n\n.modal-content {\n  background: $bg-color-white;\n  border-radius: $border-radius-lg;\n  width: 700rpx;\n  max-width: 90vw;\n  max-height: 80vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: $spacing-lg;\n  border-bottom: 1rpx solid $border-color-light;\n}\n\n.modal-title {\n  font-size: $font-size-lg;\n  font-weight: bold;\n  color: $text-color-primary;\n}\n\n.modal-close {\n  font-size: $font-size-xxl;\n  color: $text-color-secondary;\n  line-height: 1;\n}\n\n.modal-body {\n  flex: 1;\n  padding: $spacing-lg;\n  overflow-y: auto;\n}\n\n.form-item {\n  margin-bottom: $spacing-lg;\n}\n\n.form-label {\n  font-size: $font-size-base;\n  color: $text-color-primary;\n  margin-bottom: $spacing-sm;\n  font-weight: 500;\n}\n\n.form-input {\n  width: 100%;\n  height: 88rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: 0 $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n  \n  &:focus {\n    border-color: $primary-color;\n  }\n}\n\n.radio-group,\n.checkbox-group {\n  display: flex;\n  gap: $spacing-lg;\n}\n\n.radio-item,\n.checkbox-item {\n  display: flex;\n  align-items: center;\n  font-size: $font-size-base;\n  color: $text-color-primary;\n}\n\n.picker-input {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: 0 $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n  \n  .placeholder {\n    color: $text-color-placeholder;\n  }\n  \n  .picker-arrow {\n    color: $text-color-secondary;\n    font-size: $font-size-lg;\n  }\n}\n\n.approvers-input {\n  display: flex;\n  gap: $spacing-sm;\n  margin-bottom: $spacing-sm;\n}\n\n.approver-input {\n  flex: 1;\n  height: 80rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: 0 $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n}\n\n.add-approver-btn {\n  background: $primary-color;\n  color: $text-color-white;\n  border: none;\n  border-radius: $border-radius-base;\n  padding: 0 $spacing-base;\n  font-size: $font-size-sm;\n}\n\n.approvers-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: $spacing-sm;\n}\n\n.approver-tag {\n  display: flex;\n  align-items: center;\n  padding: 4rpx 12rpx;\n  background: $primary-color;\n  color: $text-color-white;\n  border-radius: 8rpx;\n  font-size: $font-size-xs;\n}\n\n.department-list {\n  max-height: 400rpx;\n  overflow-y: auto;\n}\n\n.department-item {\n  display: flex;\n  align-items: center;\n  padding: $spacing-base;\n  border-bottom: 1rpx solid $border-color-light;\n  font-size: $font-size-base;\n  color: $text-color-primary;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  &.all-departments {\n    font-weight: 500;\n    color: $primary-color;\n  }\n}\n\n.department-divider {\n  height: 1rpx;\n  background: $border-color;\n  margin: $spacing-sm 0;\n}\n\n.modal-footer {\n  display: flex;\n  gap: $spacing-base;\n  padding: $spacing-lg;\n  border-top: 1rpx solid $border-color-light;\n}\n\n.modal-btn {\n  flex: 1;\n  height: 80rpx;\n  border: none;\n  border-radius: $border-radius-base;\n  font-size: $font-size-base;\n  \n  &.cancel-btn {\n    background: $bg-color-light;\n    color: $text-color-secondary;\n  }\n  \n  &.confirm-btn {\n    background: $primary-color;\n    color: $text-color-white;\n    \n    &:disabled {\n      opacity: 0.5;\n    }\n  }\n}\n\n/* 修复picker在modal中的层级问题 */\n.modal-overlay {\n  z-index: 999 !important;\n}\n\n.modal-content {\n  z-index: 1000 !important;\n  position: relative;\n}\n\n.modal-content picker {\n  z-index: 1002 !important;\n  position: relative;\n}\n\n.modal-content .picker-input {\n  position: relative;\n  z-index: 1002 !important;\n}\n\n/* 全局picker层级修复 */\n:deep(.uni-picker-container) {\n  z-index: 1003 !important;\n}\n\n:deep(.uni-picker) {\n  z-index: 1003 !important;\n}\n\n/* 节点移动动画 */\n.approval-node {\n  transition: all 0.3s ease;\n}\n\n/* 用户选择样式 */\n.users-list {\n  max-height: 400rpx;\n  overflow-y: auto;\n}\n\n.user-item {\n  display: flex;\n  align-items: center;\n  padding: $spacing-base;\n  border-bottom: 2rpx solid $border-color;\n  cursor: pointer;\n  transition: background-color $transition-fast;\n}\n\n.user-item:hover {\n  background: $bg-color-light;\n}\n\n.user-item:active {\n  background: $bg-color-hover;\n}\n\n.user-item checkbox {\n  margin-right: $spacing-base;\n}\n</style>\n", "import MiniProgramPage from 'E:/code/ssl_gongdan/client/pages/admin/process-design.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAyUA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,aAAa;AAAA;AAAA,MAGb,2BAA2B,CAAE;AAAA;AAAA,MAC7B,eAAe,CAAE;AAAA;AAAA;AAAA,MAGjB,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,eAAe;AAAA;AAAA,MAGf,UAAU;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA;AAAA,QACN,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,WAAW,CAAE;AAAA,QACb,eAAe;AAAA,QACf,aAAa;AAAA,MACd;AAAA,MAED,iBAAiB;AAAA,MACjB,yBAAyB;AAAA;AAAA,MAGzB,gBAAgB,CAAE;AAAA,MAClB,UAAU,CAAC;AAAA,IACb;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,yBAAyB;AACvB,UAAI,KAAK,2BAA2B,GAAG;AACrC,eAAO,KAAK,eAAe,KAAK,uBAAuB,EAAE;AAAA,MAC3D;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,2BAA2B;AACzB,aAAO,KAAK,eAAe,SAAS,KAC7B,KAAK,0BAA0B,WAAW,KAAK,eAAe;AAAA,IACvE;AAAA,EACD;AAAA,EAED,MAAM,OAAO,SAAS;AACpB,SAAK,YAAY,QAAQ;AACzB,SAAK,cAAc,mBAAmB,QAAQ,QAAQ,OAAO;AAG7D,UAAM,KAAK,gBAAgB;AAC3B,UAAM,KAAK,UAAU;AACrB,UAAM,KAAK,kBAAkB;AAAA,EAC9B;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,kBAAkB;AACtB,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,KAAK,WAAW,KAAK;AAC/CA,sBAAAA,MAAY,MAAA,OAAA,yCAAA,YAAY,MAAM;AAC9B,YAAI,OAAO,SAAS,KAAK;AAEvB,cAAI,OAAO,QAAQ,OAAO,KAAK,MAAM;AACnC,iBAAK,iBAAiB,OAAO,KAAK;AAAA,iBAC7B;AACL,iBAAK,iBAAiB,OAAO,QAAQ,CAAC;AAAA,UACxC;AACAA,wBAAA,MAAA,MAAA,OAAA,yCAAY,SAAS,KAAK,cAAc;AAAA,QAC1C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,yCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,YAAY;AAChB,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,KAAK,KAAK,QAAQ;AAC5CA,sBAAAA,MAAY,MAAA,OAAA,yCAAA,YAAY,MAAM;AAC9B,YAAI,OAAO,SAAS,KAAK;AAEvB,cAAI,OAAO,QAAQ,OAAO,KAAK,MAAM;AACnC,iBAAK,WAAW,OAAO,KAAK;AAAA,iBACvB;AACL,iBAAK,WAAW,OAAO,QAAQ,CAAC;AAAA,UAClC;AACAA,wBAAY,MAAA,MAAA,OAAA,yCAAA,SAAS,KAAK,QAAQ;AAAA,QACpC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,yCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,KAAK,QAAQ,UAAU,KAAK,SAAS;AAC/DA,sBAAAA,MAAA,MAAA,OAAA,yCAAY,cAAc,MAAM;AAChC,YAAI,OAAO,SAAS,KAAK;AACvB,gBAAM,UAAU,OAAO;AACvB,gBAAM,QAAQ,QAAQ,SAAS,CAAC;AAGhC,eAAK,qBAAqB,QAAQ,eAAe;AAGjD,cAAI,MAAM,SAAS,KAAK,MAAM,CAAC,EAAE,SAAS,UAAU;AAClD,kBAAM,aAAa,MAAM,CAAC;AAE1B,kBAAM,gBAAgB,WAAW,eAAe,CAAC;AACjD,iBAAK,4BAA4B,KAAK,eAAe;AAAA,cAAO,UAC1D,cAAc,SAAS,KAAK,EAAE;AAAA,YAChC;AAEA,iBAAK,gBAAgB,MAAM,MAAM,CAAC;AAAA,iBAC7B;AAEL,iBAAK,4BAA4B,CAAC;AAClC,iBAAK,gBAAgB,CAAC;AAAA,UACxB;AAEAA,wBAAA,MAAA,MAAA,OAAA,yCAAY,YAAY,KAAK,yBAAyB;AACtDA,wBAAY,MAAA,MAAA,OAAA,yCAAA,YAAY,KAAK,aAAa;AAAA,QAC5C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,yCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB,MAAM;AACpB,YAAM,UAAU;AAAA,QACd,cAAc;AAAA,QACd,UAAU;AAAA,MACZ;AACA,aAAO,QAAQ,IAAI,KAAK;AAAA,IACzB;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,aAAa;AAClB,WAAK,gBAAgB;AACrB,WAAK,WAAW;AAAA,QACd,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,WAAW,CAAE;AAAA,QACb,eAAe;AAAA,QACf,aAAa;AAAA,MACf;AACA,WAAK,kBAAkB;AACvB,WAAK,0BAA0B;AAC/B,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,SAAS,OAAO;AACd,WAAK,aAAa;AAClB,WAAK,gBAAgB;AACrB,YAAM,OAAO,KAAK,cAAc,KAAK;AAErC,WAAK,WAAW;AAAA,QACd,MAAM,KAAK,QAAQ;AAAA,QACnB,MAAM,KAAK;AAAA,QACX,cAAc,KAAK,gBAAgB;AAAA,QACnC,gBAAgB,KAAK,kBAAkB;AAAA,QACvC,WAAW,CAAC,GAAI,KAAK,aAAa,CAAA,CAAI;AAAA,QACtC,eAAe,KAAK,kBAAkB;AAAA,QACtC,aAAa,KAAK,gBAAgB;AAAA,MACpC;AAGA,UAAI,KAAK,cAAc;AACrB,aAAK,0BAA0B,KAAK,eAAe;AAAA,UACjD,UAAQ,KAAK,OAAO,KAAK;AAAA,QAC3B;AAAA,aACK;AACL,aAAK,0BAA0B;AAAA,MACjC;AAEA,WAAK,kBAAkB;AACvB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,WAAW,OAAO;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,cAAc,OAAO,OAAO,CAAC;AAAA,UACpC;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,eAAe,MAAM;AACnB,WAAK,SAAS,OAAO;AAGrB,WAAK,SAAS,eAAe;AAC7B,WAAK,SAAS,iBAAiB;AAC/B,WAAK,SAAS,YAAY,CAAC;AAC3B,WAAK,0BAA0B;AAAA,IAChC;AAAA;AAAA,IAGD,iBAAiB,GAAG;AAClB,WAAK,SAAS,OAAO,EAAE,OAAO;AAE9B,WAAK,SAAS,eAAe;AAC7B,WAAK,SAAS,iBAAiB;AAC/B,WAAK,SAAS,YAAY,CAAC;AAC3B,WAAK,0BAA0B;AAAA,IAChC;AAAA;AAAA,IAGD,mBAAmB,GAAG;AACpB,WAAK,0BAA0B,EAAE,OAAO;AACxC,YAAM,OAAO,KAAK,eAAe,EAAE,OAAO,KAAK;AAC/C,WAAK,SAAS,eAAe,KAAK;AAClC,WAAK,SAAS,iBAAiB,KAAK;AAAA,IACrC;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,WAAW,KAAK,gBAAgB,KAAK;AAC3C,UAAI,CAAC,UAAU;AACbA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,UAAU,SAAS,QAAQ,GAAG;AAC9CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,WAAK,SAAS,UAAU,KAAK,QAAQ;AACrC,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,aAAO,KAAK,SAAS,UAAU,SAAS,MAAM;AAAA,IAC/C;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,YAAM,SAAS,SAAS,EAAE,OAAO,KAAK;AAEtC,UAAI,EAAE,OAAO,SAAS;AACpB,YAAI,CAAC,KAAK,eAAe,MAAM,GAAG;AAChC,eAAK,SAAS,UAAU,KAAK,MAAM;AAAA,QACrC;AAAA,aACK;AACL,aAAK,SAAS,YAAY,KAAK,SAAS,UAAU,OAAO,QAAM,OAAO,MAAM;AAAA,MAC9E;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB,QAAQ;AACzB,YAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,OAAO,MAAM;AACpD,aAAO,OAAO,GAAG,KAAK,cAAc,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM;AAAA,IAClE;AAAA;AAAA,IAGD,WAAW,OAAO;AAChB,WAAK,SAAS,UAAU,OAAO,OAAO,CAAC;AAAA,IACxC;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,WAAK,SAAS,UAAU,OAAO,OAAO,CAAC;AAAA,IACxC;AAAA;AAAA,IAGD,sBAAsB,GAAG;AACvB,WAAK,SAAS,gBAAgB,EAAE,OAAO,MAAM,SAAS;AAAA,IACvD;AAAA;AAAA,IAGD,oBAAoB,GAAG;AACrB,WAAK,SAAS,cAAc,EAAE,OAAO,MAAM,SAAS;AAAA,IACrD;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,CAAC,KAAK,SAAS,KAAK,KAAI,GAAI;AAC9B,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,SAAS,gBAAgB,CAAC,KAAK,SAAS,cAAc;AACtE,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,SAAS,YAAY,KAAK,SAAS,UAAU,WAAW,GAAG;AAC3E,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,CAAC,KAAK,oBAAoB;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,YAAM,WAAW,EAAE,GAAG,KAAK,SAAS;AAEpC,UAAI,KAAK,YAAY;AACnB,aAAK,cAAc,OAAO,KAAK,eAAe,GAAG,QAAQ;AAAA,aACpD;AACL,aAAK,cAAc,KAAK,QAAQ;AAAA,MAClC;AAEA,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,6BAA6B;AAC3B,WAAK,sBAAsB;AAAA,IAC5B;AAAA;AAAA,IAGD,sBAAsB;AACpB,WAAK,sBAAsB;AAAA,IAC5B;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,YAAM,SAAS,KAAK;AACpB,UAAI,KAAK,eAAe,MAAM,GAAG;AAE/B,aAAK,SAAS,YAAY,KAAK,SAAS,UAAU,OAAO,OAAK,EAAE,OAAO,MAAM;AAAA,aACxE;AAEL,aAAK,SAAS,UAAU,KAAK;AAAA,UAC3B,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,gBAAgB,KAAK;AAAA,SACtB;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,2BAA2B,QAAQ;AACjC,aAAO,KAAK,0BAA0B,KAAK,UAAQ,KAAK,OAAO,MAAM;AAAA,IACtE;AAAA;AAAA,IAGD,uBAAuB,GAAG;AACxB,UAAI,EAAE,OAAO,SAAS;AAEpB,aAAK,4BAA4B,CAAC,GAAG,KAAK,cAAc;AAAA,aACnD;AAEL,aAAK,4BAA4B,CAAC;AAAA,MACpC;AAAA,IACD;AAAA;AAAA,IAGD,uBAAuB,MAAM;AAC3BA,oBAAAA,MAAY,MAAA,OAAA,yCAAA,WAAW,IAAI;AAE3B,UAAI,KAAK,2BAA2B,KAAK,EAAE,GAAG;AAE5C,aAAK,4BAA4B,KAAK,0BAA0B;AAAA,UAC9D,OAAK,EAAE,OAAO,KAAK;AAAA,QACrB;AAAA,aACK;AAEL,aAAK,0BAA0B,KAAK,IAAI;AAAA,MAC1C;AAEAA,oBAAY,MAAA,MAAA,OAAA,yCAAA,YAAY,KAAK,yBAAyB;AAAA,IACvD;AAAA;AAAA,IAGD,yBAAyB,GAAG;AAC1B,YAAM,SAAS,SAAS,EAAE,OAAO,KAAK;AACtC,YAAM,OAAO,KAAK,eAAe,KAAK,OAAK,EAAE,OAAO,MAAM;AAE1DA,oBAAAA,4DAAY,WAAW;AAAA,QACrB;AAAA,QACA;AAAA,QACA,SAAS,EAAE,OAAO;AAAA,QAClB,iBAAiB,KAAK;AAAA,OACvB;AAED,UAAI,EAAE,OAAO,SAAS;AACpB,YAAI,CAAC,KAAK,2BAA2B,MAAM,GAAG;AAC5C,eAAK,0BAA0B,KAAK,IAAI;AAAA,QAC1C;AAAA,aACK;AACL,aAAK,4BAA4B,KAAK,0BAA0B;AAAA,UAC9D,OAAK,EAAE,OAAO;AAAA,QAChB;AAAA,MACF;AAEAA,oBAAY,MAAA,MAAA,OAAA,yCAAA,YAAY,KAAK,yBAAyB;AAAA,IACvD;AAAA;AAAA,IAGD,uBAAuB,QAAQ;AAC7B,WAAK,4BAA4B,KAAK,0BAA0B;AAAA,QAC9D,UAAQ,KAAK,OAAO;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClBA,gFAAY,iBAAiB,KAAK,yBAAyB;AAC3DA,oBAAY,MAAA,MAAA,OAAA,yCAAA,gBAAgB,KAAK,0BAA0B,MAAM;AAEjE,UAAI,KAAK,0BAA0B,WAAW,GAAG;AAC/CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,KAAK,cAAc,WAAW,GAAG;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,WAAK,cAAc;AAEnB,UAAI;AAEF,cAAM,iBAAiB,KAAK,cAAc,IAAI,UAAQ;AACpD,gBAAM,gBAAgB;AAAA,YACpB,MAAM;AAAA;AAAA,YACN,MAAM,KAAK,QAAQ;AAAA,UACrB;AAGA,cAAI,KAAK,SAAS,cAAc;AAC9B,0BAAc,eAAe;AAC7B,0BAAc,cAAc,CAAC;AAAA,cAC3B,IAAI,KAAK;AAAA,cACT,MAAM,KAAK;AAAA,aACZ;AAAA,qBACQ,KAAK,SAAS,QAAQ;AAC/B,0BAAc,eAAe;AAC7B,0BAAc,QAAQ,KAAK,aAAa,CAAC;AAAA,UAC3C;AAEA,iBAAO;AAAA,SACR;AAED,cAAM,cAAc;AAAA,UAClB,mBAAmB,KAAK,0BAA0B,IAAI,UAAQ,KAAK,EAAE;AAAA,UACrE,OAAO;AAAA,QACT;AAEAA,sBAAAA,MAAA,MAAA,OAAA,yCAAY,YAAY,WAAW;AAEnC,cAAM,SAAS,MAAM,KAAK,KAAK,QAAQ,aAAa,KAAK,WAAW,WAAW;AAE/E,YAAI,OAAO,SAAS,KAAK;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAED,qBAAW,MAAM;AACfA,0BAAAA,MAAI,aAAa;AAAA,UAClB,GAAE,IAAI;AAAA,eACF;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,cAAc;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,OAAO;AAChB,UAAI,QAAQ,GAAG;AAEb,cAAM,aAAa,KAAK,cAAc,OAAO,OAAO,CAAC,EAAE,CAAC;AACxD,aAAK,cAAc,OAAO,QAAQ,GAAG,GAAG,UAAU;AAGlDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,UAAI,QAAQ,KAAK,cAAc,SAAS,GAAG;AAEzC,cAAM,aAAa,KAAK,cAAc,OAAO,OAAO,CAAC,EAAE,CAAC;AACxD,aAAK,cAAc,OAAO,QAAQ,GAAG,GAAG,UAAU;AAGlDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACz3BA,GAAG,WAAW,eAAe;"}