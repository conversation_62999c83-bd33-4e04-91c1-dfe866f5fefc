<template>
  <view class="process-design-container">
    <!-- 顶部信息 -->
    <view class="header-section">
      <view class="process-info">
        <text class="process-name">{{ processName }}</text>
        <text class="process-desc" v-if="processDescription">{{ processDescription }}</text>
        <text class="process-desc" v-else>流程设计</text>
      </view>
      <button class="save-btn" @click="saveProcess" :disabled="saveLoading">
        <text v-if="saveLoading">保存中...</text>
        <text v-else>保存</text>
      </button>
    </view>

    <!-- 节点列表 -->
    <view class="nodes-section">
      <view class="section-title">流程节点</view>
      
      <view class="nodes-list">
        <!-- 提交节点（固定） -->
        <view class="node-item submit-node">
          <view class="node-header">
            <view class="node-icon">
              <Icon name="edit" size="32" color="#fff" />
            </view>
            <view class="node-info">
              <text class="node-title">提交节点</text>
              <text class="node-desc">工单提交入口</text>
            </view>
            <view class="node-status">固定</view>
          </view>
          
          <view class="node-config">
            <view class="config-item">
              <text class="config-label">可提交部门：</text>
              <view class="department-tags">
                <view 
                  v-for="dept in selectedSubmitDepartments" 
                  :key="dept.id"
                  class="department-tag"
                >
                  {{ dept.name }}
                  <text class="tag-remove" @click="removeSubmitDepartment(dept.id)">×</text>
                </view>
                <button class="add-department-btn" @click="showSubmitDepartmentPicker">
                  + 添加部门
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 审批节点 -->
        <view
          v-for="(node, index) in approvalNodes"
          :key="index"
          class="node-item approval-node"
        >
          <view class="node-header">
            <view class="node-icon">
              <Icon name="user" size="32" color="#fff" />
            </view>
            <view class="node-info">
              <text class="node-title">{{ node.name || `审批节点 ${index + 1}` }}</text>
              <text class="node-desc">{{ getNodeTypeText(node.type) }}</text>
            </view>
            <view class="node-actions">
              <view class="move-buttons">
                <button
                  class="move-btn up-btn"
                  @click="moveNodeUp(index)"
                  :disabled="index === 0"
                >↑</button>
                <button
                  class="move-btn down-btn"
                  @click="moveNodeDown(index)"
                  :disabled="index === approvalNodes.length - 1"
                >↓</button>
              </view>
              <button class="node-btn edit-btn" @click="editNode(index)">编辑</button>
              <button class="node-btn delete-btn" @click="deleteNode(index)">删除</button>
            </view>
          </view>
          
          <view class="node-config">
            <view class="config-item">
              <text class="config-label">审批方式：</text>
              <text class="config-value">{{ getNodeTypeText(node.type) }}</text>
            </view>
            
            <view class="config-item" v-if="node.type === 'department'">
              <text class="config-label">审批部门：</text>
              <text class="config-value">{{ node.departmentName }}</text>
            </view>
            
            <view class="config-item" v-if="node.type === 'person'">
              <text class="config-label">审批人员：</text>
              <text class="config-value">{{ node.approvers.join(', ') }}</text>
            </view>
            
            <view class="config-item">
              <text class="config-label">允许转单：</text>
              <text class="config-value">{{ node.allowTransfer ? '是' : '否' }}</text>
            </view>
            
            <view class="config-item">
              <text class="config-label">允许驳回：</text>
              <text class="config-value">{{ node.allowReject ? '是' : '否' }}</text>
            </view>
          </view>
        </view>

        <!-- 添加节点按钮 -->
        <view class="add-node-section">
          <button class="add-node-btn" @click="showAddNodeModal">
            + 添加审批节点
          </button>
        </view>
      </view>
    </view>

    <!-- 流程预览 -->
    <view class="preview-section">
      <view class="section-title">流程预览</view>
      <view class="flow-preview">
        <view class="flow-step">
          <view class="step-icon submit">
            <Icon name="edit" size="28" color="#fff" />
          </view>
          <text class="step-text">提交工单</text>
        </view>
        
        <view v-for="(node, index) in approvalNodes" :key="index" class="flow-step">
          <view class="step-arrow">↓</view>
          <view class="step-icon approval">
            <Icon name="user" size="28" color="#fff" />
          </view>
          <text class="step-text">{{ node.name || `审批节点${index + 1}` }}</text>
        </view>
        
        <view class="flow-step">
          <view class="step-arrow">↓</view>
          <view class="step-icon complete">
            <Icon name="check" size="28" color="#fff" />
          </view>
          <text class="step-text">完成</text>
        </view>
      </view>
    </view>

    <!-- 添加/编辑节点弹窗 -->
    <view class="modal-overlay" v-if="showNodeModal" @click="hideNodeModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ isEditNode ? '编辑节点' : '添加节点' }}</text>
          <text class="modal-close" @click="hideNodeModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <view class="form-label">节点名称</view>
            <input 
              class="form-input" 
              type="text" 
              placeholder="请输入节点名称"
              v-model="nodeForm.name"
              maxlength="20"
            />
          </view>
          
          <view class="form-item">
            <view class="form-label">审批类型</view>
            <view class="radio-group">
              <label class="radio-item" @click="selectNodeType('department')">
                <radio value="department" :checked="nodeForm.type === 'department'" />
                <text>部门审批</text>
              </label>
              <label class="radio-item" @click="selectNodeType('person')">
                <radio value="person" :checked="nodeForm.type === 'person'" />
                <text>个人审批</text>
              </label>
            </view>
          </view>
          
          <view class="form-item" v-if="nodeForm.type === 'department'">
            <view class="form-label">选择部门</view>
            <picker 
              mode="selector" 
              :range="departmentList" 
              range-key="name"
              @change="onDepartmentChange"
              :value="selectedDepartmentIndex"
            >
              <view class="picker-input">
                <text v-if="nodeForm.departmentId">{{ selectedDepartmentName }}</text>
                <text v-else class="placeholder">请选择部门</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="form-item" v-if="nodeForm.type === 'person'">
            <view class="form-label">选择审批人员</view>
            <view class="picker-input" @click="showUserModal = true">
              <text v-if="nodeForm.approvers.length > 0">已选择 {{ nodeForm.approvers.length }} 人</text>
              <text v-else class="placeholder">请选择审批人员</text>
              <text class="picker-arrow">></text>
            </view>
            <view class="selected-users" v-if="nodeForm.approvers.length > 0">
              <text class="selected-label">已选择：</text>
              <view
                v-for="(userId, index) in nodeForm.approvers"
                :key="index"
                class="user-tag"
              >
                {{ getUserDisplayName(userId) }}
                <text class="tag-remove" @click="removeUser(index)">×</text>
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <view class="form-label">节点设置</view>
            <view class="checkbox-group">
              <label class="checkbox-item">
                <checkbox :checked="nodeForm.allowTransfer" @change="onAllowTransferChange" />
                <text>允许转单</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="nodeForm.allowReject" @change="onAllowRejectChange" />
                <text>允许驳回</text>
              </label>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @click="hideNodeModal">取消</button>
          <button 
            class="modal-btn confirm-btn" 
            @click="saveNode"
            :disabled="!validateNodeForm()"
          >
            {{ isEditNode ? '保存' : '添加' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 部门选择弹窗 -->
    <view class="modal-overlay" v-if="showDepartmentModal" @click="hideDepartmentModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择可提交部门</text>
          <text class="modal-close" @click="hideDepartmentModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="department-list">
            <!-- 全部部门选项 -->
            <label class="department-item all-departments">
              <checkbox
                value="all"
                :checked="isAllDepartmentsSelected"
                @change="onAllDepartmentsChange"
              />
              <text>全部部门</text>
            </label>

            <!-- 分隔线 -->
            <view class="department-divider"></view>

            <!-- 具体部门列表 -->
            <label
              v-for="dept in departmentList"
              :key="dept.id"
              class="department-item"
              @click="toggleSubmitDepartment(dept)"
            >
              <checkbox
                :value="String(dept.id)"
                :checked="isSubmitDepartmentSelected(dept.id)"
              />
              <text>{{ dept.name }}</text>
            </label>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn confirm-btn" @click="hideDepartmentModal">确定</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 用户选择模态框 -->
  <view v-if="showUserModal" class="modal-overlay" @click="hideUserModal">
    <view class="modal-content" @click.stop>
      <view class="modal-header">
        <text class="modal-title">选择审批人员</text>
        <text class="modal-close" @click="hideUserModal">×</text>
      </view>

      <view class="modal-body">
        <view class="users-list">
          <label
            v-for="user in userList"
            :key="user.id"
            class="user-item"
            @click="toggleUser(user)"
          >
            <checkbox
              :value="String(user.id)"
              :checked="isUserSelected(user.id)"
            />
            <text>{{ user.departmentName }}-{{ user.name }}</text>
          </label>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn confirm-btn" @click="hideUserModal">确定</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      processId: '',
      processName: '',
      processDescription: '',
      saveLoading: false,
      
      // 节点数据
      selectedSubmitDepartments: [], // 可提交部门
      approvalNodes: [], // 审批节点
      
      // 弹窗状态
      showNodeModal: false,
      showDepartmentModal: false,
      showUserModal: false,
      isEditNode: false,
      editNodeIndex: -1,
      
      // 表单数据
      nodeForm: {
        name: '',
        type: 'department', // department | person
        departmentId: '',
        departmentName: '',
        approvers: [],
        allowTransfer: true,
        allowReject: true
      },
      
      currentApprover: '',
      selectedDepartmentIndex: -1,
      
      // 基础数据
      departmentList: [],
      userList: []
    }
  },
  
  computed: {
    selectedDepartmentName() {
      if (this.selectedDepartmentIndex >= 0) {
        return this.departmentList[this.selectedDepartmentIndex].name
      }
      return ''
    },

    // 是否选择了全部部门
    isAllDepartmentsSelected() {
      return this.departmentList.length > 0 &&
             this.selectedSubmitDepartments.length === this.departmentList.length
    }
  },
  
  async onLoad(options) {
    this.processId = options.id
    this.processName = decodeURIComponent(options.name || '未命名流程')

    // 先加载基础数据，再加载流程详情
    await this.loadDepartments()
    await this.loadUsers()
    await this.loadProcessDetail()
  },
  
  methods: {
    // 加载部门列表
    async loadDepartments() {
      try {
        const result = await this.$api.department.list()
        console.log('部门API返回:', result)
        if (result.code === 200) {
          // 检查数据结构
          if (result.data && result.data.list) {
            this.departmentList = result.data.list
          } else {
            this.departmentList = result.data || []
          }
          console.log('部门列表:', this.departmentList)
        }
      } catch (error) {
        console.error('加载部门列表失败:', error)
      }
    },

    // 加载用户列表
    async loadUsers() {
      try {
        const result = await this.$api.user.getList()
        console.log('用户API返回:', result)
        if (result.code === 200) {
          // 检查数据结构
          if (result.data && result.data.list) {
            this.userList = result.data.list
          } else {
            this.userList = result.data || []
          }
          console.log('用户列表:', this.userList)
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
      }
    },

    // 加载流程详情
    async loadProcessDetail() {
      try {
        const result = await this.$api.process.getDetail(this.processId)
        console.log('流程详情API返回:', result)
        if (result.code === 200) {
          const process = result.data
          const nodes = process.nodes || []

          // 设置流程描述
          this.processDescription = process.description || ''

          // 如果有节点，从第一个节点（提交节点）获取部门信息
          if (nodes.length > 0 && nodes[0].type === 'submit') {
            const submitNode = nodes[0]
            // 根据部门ID数组找到对应的部门对象
            const departmentIds = submitNode.departments || []
            this.selectedSubmitDepartments = this.departmentList.filter(dept =>
              departmentIds.includes(dept.id)
            )
            // 审批节点从第二个开始
            this.approvalNodes = nodes.slice(1)
          } else {
            // 如果没有节点，初始化为空
            this.selectedSubmitDepartments = []
            this.approvalNodes = []
          }

          console.log('加载的提交部门:', this.selectedSubmitDepartments)
          console.log('加载的审批节点:', this.approvalNodes)
        }
      } catch (error) {
        console.error('加载流程详情失败:', error)
      }
    },
    
    // 获取节点类型文本
    getNodeTypeText(type) {
      const typeMap = {
        'department': '部门审批',
        'person': '个人审批'
      }
      return typeMap[type] || '未知类型'
    },
    
    // 显示添加节点弹窗
    showAddNodeModal() {
      this.isEditNode = false
      this.editNodeIndex = -1
      this.nodeForm = {
        name: '',
        type: 'department',
        departmentId: '',
        departmentName: '',
        approvers: [],
        allowTransfer: true,
        allowReject: true
      }
      this.currentApprover = ''
      this.selectedDepartmentIndex = -1
      this.showNodeModal = true
    },
    
    // 编辑节点
    editNode(index) {
      this.isEditNode = true
      this.editNodeIndex = index
      const node = this.approvalNodes[index]
      
      this.nodeForm = {
        name: node.name || '',
        type: node.type,
        departmentId: node.departmentId || '',
        departmentName: node.departmentName || '',
        approvers: [...(node.approvers || [])],
        allowTransfer: node.allowTransfer !== false,
        allowReject: node.allowReject !== false
      }
      
      // 设置部门选择器索引
      if (node.departmentId) {
        this.selectedDepartmentIndex = this.departmentList.findIndex(
          dept => dept.id === node.departmentId
        )
      } else {
        this.selectedDepartmentIndex = -1
      }
      
      this.currentApprover = ''
      this.showNodeModal = true
    },
    
    // 删除节点
    deleteNode(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个审批节点吗？',
        success: (res) => {
          if (res.confirm) {
            this.approvalNodes.splice(index, 1)
          }
        }
      })
    },
    
    // 隐藏节点弹窗
    hideNodeModal() {
      this.showNodeModal = false
    },
    
    // 选择节点类型（单选，不能取消）
    selectNodeType(type) {
      this.nodeForm.type = type

      // 清空相关字段
      this.nodeForm.departmentId = ''
      this.nodeForm.departmentName = ''
      this.nodeForm.approvers = []
      this.selectedDepartmentIndex = -1
    },

    // 节点类型变化（保留原方法以防其他地方使用）
    onNodeTypeChange(e) {
      this.nodeForm.type = e.detail.value
      // 清空相关字段
      this.nodeForm.departmentId = ''
      this.nodeForm.departmentName = ''
      this.nodeForm.approvers = []
      this.selectedDepartmentIndex = -1
    },
    
    // 部门选择变化
    onDepartmentChange(e) {
      this.selectedDepartmentIndex = e.detail.value
      const dept = this.departmentList[e.detail.value]
      this.nodeForm.departmentId = dept.id
      this.nodeForm.departmentName = dept.name
    },
    
    // 添加审批人
    addApprover() {
      const approver = this.currentApprover.trim()
      if (!approver) {
        uni.showToast({
          title: '请输入工号',
          icon: 'none'
        })
        return
      }
      
      if (this.nodeForm.approvers.includes(approver)) {
        uni.showToast({
          title: '工号已存在',
          icon: 'none'
        })
        return
      }
      
      this.nodeForm.approvers.push(approver)
      this.currentApprover = ''
    },
    
    // 检查用户是否已选择
    isUserSelected(userId) {
      return this.nodeForm.approvers.includes(userId)
    },

    // 用户选择变化
    onUserChange(e) {
      const userId = parseInt(e.detail.value)

      if (e.detail.checked) {
        if (!this.isUserSelected(userId)) {
          this.nodeForm.approvers.push(userId)
        }
      } else {
        this.nodeForm.approvers = this.nodeForm.approvers.filter(id => id !== userId)
      }
    },

    // 获取用户显示名称
    getUserDisplayName(userId) {
      const user = this.userList.find(u => u.id === userId)
      return user ? `${user.departmentName}-${user.name}` : `用户${userId}`
    },

    // 移除用户
    removeUser(index) {
      this.nodeForm.approvers.splice(index, 1)
    },

    // 移除审批人（保留兼容性）
    removeApprover(index) {
      this.nodeForm.approvers.splice(index, 1)
    },
    
    // 允许转单变化
    onAllowTransferChange(e) {
      this.nodeForm.allowTransfer = e.detail.value.length > 0
    },
    
    // 允许驳回变化
    onAllowRejectChange(e) {
      this.nodeForm.allowReject = e.detail.value.length > 0
    },
    
    // 验证节点表单
    validateNodeForm() {
      if (!this.nodeForm.name.trim()) {
        return false
      }
      
      if (this.nodeForm.type === 'department' && !this.nodeForm.departmentId) {
        return false
      }
      
      if (this.nodeForm.type === 'person' && this.nodeForm.approvers.length === 0) {
        return false
      }
      
      return true
    },
    
    // 保存节点
    saveNode() {
      if (!this.validateNodeForm()) {
        uni.showToast({
          title: '请完善节点信息',
          icon: 'none'
        })
        return
      }
      
      const nodeData = { ...this.nodeForm }
      
      if (this.isEditNode) {
        this.approvalNodes.splice(this.editNodeIndex, 1, nodeData)
      } else {
        this.approvalNodes.push(nodeData)
      }
      
      this.hideNodeModal()
    },
    
    // 显示提交部门选择弹窗
    showSubmitDepartmentPicker() {
      this.showDepartmentModal = true
    },
    
    // 隐藏部门选择弹窗
    hideDepartmentModal() {
      this.showDepartmentModal = false
    },

    // 显示用户选择弹窗
    showUserPicker() {
      this.showUserModal = true
    },

    // 隐藏用户选择弹窗
    hideUserModal() {
      this.showUserModal = false
    },

    // 切换用户选择
    toggleUser(user) {
      const userId = user.id
      if (this.isUserSelected(userId)) {
        // 如果已选择，则移除
        this.nodeForm.approvers = this.nodeForm.approvers.filter(u => u.id !== userId)
      } else {
        // 如果未选择，则添加
        this.nodeForm.approvers.push({
          id: user.id,
          name: user.name,
          departmentName: user.departmentName
        })
      }
    },
    
    // 检查提交部门是否已选择
    isSubmitDepartmentSelected(deptId) {
      return this.selectedSubmitDepartments.some(dept => dept.id === deptId)
    },
    
    // 全部部门选择变化
    onAllDepartmentsChange(e) {
      if (e.detail.checked) {
        // 选择全部部门
        this.selectedSubmitDepartments = [...this.departmentList]
      } else {
        // 取消全部部门
        this.selectedSubmitDepartments = []
      }
    },

    // 切换提交部门选择
    toggleSubmitDepartment(dept) {
      console.log('切换部门选择:', dept)

      if (this.isSubmitDepartmentSelected(dept.id)) {
        // 如果已选择，则移除
        this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(
          d => d.id !== dept.id
        )
      } else {
        // 如果未选择，则添加
        this.selectedSubmitDepartments.push(dept)
      }

      console.log('部门选择变化后:', this.selectedSubmitDepartments)
    },

    // 提交部门选择变化（保留兼容性）
    onSubmitDepartmentChange(e) {
      const deptId = parseInt(e.detail.value) // 转换为数字
      const dept = this.departmentList.find(d => d.id === deptId)

      console.log('部门选择变化:', {
        deptId,
        dept,
        checked: e.detail.checked,
        currentSelected: this.selectedSubmitDepartments
      })

      if (e.detail.checked) {
        if (!this.isSubmitDepartmentSelected(deptId)) {
          this.selectedSubmitDepartments.push(dept)
        }
      } else {
        this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(
          d => d.id !== deptId
        )
      }

      console.log('部门选择变化后:', this.selectedSubmitDepartments)
    },
    
    // 移除提交部门
    removeSubmitDepartment(deptId) {
      this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(
        dept => dept.id !== deptId
      )
    },
    
    // 保存流程
    async saveProcess() {
      console.log('保存流程 - 选中的部门:', this.selectedSubmitDepartments)
      console.log('保存流程 - 部门数量:', this.selectedSubmitDepartments.length)

      if (this.selectedSubmitDepartments.length === 0) {
        uni.showToast({
          title: '请至少选择一个可提交部门',
          icon: 'none'
        })
        return
      }
      
      if (this.approvalNodes.length === 0) {
        uni.showToast({
          title: '请至少添加一个审批节点',
          icon: 'none'
        })
        return
      }
      
      this.saveLoading = true
      
      try {
        // 格式化节点数据
        const formattedNodes = this.approvalNodes.map(node => {
          const formattedNode = {
            type: 'approve', // 统一设置为审批节点
            name: node.name || '审批节点'
          }

          // 根据审批类型设置相应字段
          if (node.type === 'department') {
            formattedNode.approvalType = 'department'
            formattedNode.departments = [{
              id: node.departmentId,
              name: node.departmentName
            }]
          } else if (node.type === 'user') {
            formattedNode.approvalType = 'user'
            formattedNode.users = node.approvers || []
          }

          return formattedNode
        })

        const processData = {
          submitDepartments: this.selectedSubmitDepartments.map(dept => dept.id),
          nodes: formattedNodes
        }

        console.log('发送的流程数据:', processData)
        
        const result = await this.$api.process.updateDesign(this.processId, processData)
        
        if (result.code === 200) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: result.message || '保存失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('保存流程失败:', error)
        uni.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        })
      } finally {
        this.saveLoading = false
      }
    },

    // 上移节点
    moveNodeUp(index) {
      if (index > 0) {
        // 使用数组splice方法确保响应式更新
        const nodeToMove = this.approvalNodes.splice(index, 1)[0]
        this.approvalNodes.splice(index - 1, 0, nodeToMove)

        // 添加移动提示
        uni.showToast({
          title: '节点已上移',
          icon: 'success',
          duration: 1000
        })
      }
    },

    // 下移节点
    moveNodeDown(index) {
      if (index < this.approvalNodes.length - 1) {
        // 使用数组splice方法确保响应式更新
        const nodeToMove = this.approvalNodes.splice(index, 1)[0]
        this.approvalNodes.splice(index + 1, 0, nodeToMove)

        // 添加移动提示
        uni.showToast({
          title: '节点已下移',
          icon: 'success',
          duration: 1000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.process-design-container {
  min-height: 100vh;
  background: $bg-color-page;
  padding-bottom: 120rpx;
}

.header-section {
  background: $bg-color-white;
  padding: $spacing-lg;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-base;
  box-shadow: $box-shadow-light;
}

.process-info {
  flex: 1;
}

.process-name {
  display: block;
  font-size: $font-size-xl;
  font-weight: bold;
  color: $text-color-primary;
  margin-bottom: $spacing-xs;
}

.process-desc {
  display: block;
  font-size: $font-size-base;
  color: $text-color-secondary;
}

.save-btn {
  background: $primary-color;
  color: $text-color-white;
  border: none;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-lg;
  font-size: $font-size-base;
  
  &:disabled {
    opacity: 0.6;
  }
}

.nodes-section,
.preview-section {
  background: $bg-color-white;
  margin-bottom: $spacing-base;
  box-shadow: $box-shadow-light;
}

.section-title {
  font-size: $font-size-lg;
  font-weight: bold;
  color: $text-color-primary;
  padding: $spacing-lg $spacing-lg $spacing-base;
  border-bottom: 1rpx solid $border-color-light;
}

.nodes-list {
  padding: $spacing-lg;
}

.node-item {
  border: 2rpx solid $border-color;
  border-radius: $border-radius-base;
  margin-bottom: $spacing-base;
  overflow: hidden;
  
  &.submit-node {
    border-color: $primary-color;
  }
  
  &.approval-node {
    border-color: $warning-color;
  }
}

.node-header {
  display: flex;
  align-items: center;
  padding: $spacing-base;
  background: $bg-color-light;
  border-bottom: 1rpx solid $border-color-light;
}

.node-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: $primary-color;
  color: $text-color-white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-lg;
  margin-right: $spacing-base;
}

.node-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.node-title {
  font-size: $font-size-base;
  font-weight: bold;
  color: $text-color-primary;
  margin-bottom: 4rpx;
}

.node-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.node-status {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: $font-size-xs;
  background: $bg-color-light;
  color: $text-color-secondary;
}

.node-actions {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.move-buttons {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.move-btn {
  width: 48rpx;
  height: 32rpx;
  border: 1rpx solid $border-color;
  border-radius: $border-radius-sm;
  background: $bg-color-white;
  color: $text-color-secondary;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:not(:disabled):active {
    background: $bg-color-light;
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
}

.node-btn {
  padding: $spacing-xs $spacing-sm;
  border: none;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  
  &.edit-btn {
    background: $primary-color;
    color: $text-color-white;
  }
  
  &.delete-btn {
    background: $danger-color;
    color: $text-color-white;
  }
}

.node-config {
  padding: $spacing-base;
}

.config-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: $spacing-sm;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.config-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  min-width: 120rpx;
  flex-shrink: 0;
}

.config-value {
  font-size: $font-size-sm;
  color: $text-color-primary;
  flex: 1;
}

.department-tags {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
  align-items: center;
}

.department-tag {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  background: $primary-color;
  color: $text-color-white;
  border-radius: 8rpx;
  font-size: $font-size-xs;
}

.tag-remove {
  margin-left: $spacing-xs;
  font-size: $font-size-sm;
  cursor: pointer;
}

.add-department-btn {
  padding: 4rpx 12rpx;
  border: 2rpx dashed $border-color;
  border-radius: 8rpx;
  background: transparent;
  color: $text-color-secondary;
  font-size: $font-size-xs;
}

.add-node-section {
  text-align: center;
  padding: $spacing-lg;
}

.add-node-btn {
  background: $primary-color;
  color: $text-color-white;
  border: none;
  border-radius: $border-radius-base;
  padding: $spacing-base $spacing-lg;
  font-size: $font-size-base;
}

.flow-preview {
  padding: $spacing-lg;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: $spacing-base;
}

.step-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-lg;
  color: $text-color-white;
  margin-bottom: $spacing-sm;
  
  &.submit {
    background: $primary-color;
  }
  
  &.approval {
    background: $warning-color;
  }
  
  &.complete {
    background: $success-color;
  }
}

.step-text {
  font-size: $font-size-sm;
  color: $text-color-primary;
  text-align: center;
}

.step-arrow {
  font-size: $font-size-lg;
  color: $text-color-secondary;
  margin: $spacing-sm 0;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  // 部门选择弹窗需要更高的层级
  &:last-child {
    z-index: 1100;
  }
}

.modal-content {
  background: $bg-color-white;
  border-radius: $border-radius-lg;
  width: 700rpx;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-lg;
  border-bottom: 1rpx solid $border-color-light;
}

.modal-title {
  font-size: $font-size-lg;
  font-weight: bold;
  color: $text-color-primary;
}

.modal-close {
  font-size: $font-size-xxl;
  color: $text-color-secondary;
  line-height: 1;
}

.modal-body {
  flex: 1;
  padding: $spacing-lg;
  overflow-y: auto;
}

.form-item {
  margin-bottom: $spacing-lg;
}

.form-label {
  font-size: $font-size-base;
  color: $text-color-primary;
  margin-bottom: $spacing-sm;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid $border-color;
  border-radius: $border-radius-base;
  padding: 0 $spacing-base;
  font-size: $font-size-base;
  background: $bg-color-white;
  
  &:focus {
    border-color: $primary-color;
  }
}

.radio-group,
.checkbox-group {
  display: flex;
  gap: $spacing-lg;
}

.radio-item,
.checkbox-item {
  display: flex;
  align-items: center;
  font-size: $font-size-base;
  color: $text-color-primary;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  border: 2rpx solid $border-color;
  border-radius: $border-radius-base;
  padding: 0 $spacing-base;
  font-size: $font-size-base;
  background: $bg-color-white;
  
  .placeholder {
    color: $text-color-placeholder;
  }
  
  .picker-arrow {
    color: $text-color-secondary;
    font-size: $font-size-lg;
  }
}

.approvers-input {
  display: flex;
  gap: $spacing-sm;
  margin-bottom: $spacing-sm;
}

.approver-input {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid $border-color;
  border-radius: $border-radius-base;
  padding: 0 $spacing-base;
  font-size: $font-size-base;
  background: $bg-color-white;
}

.add-approver-btn {
  background: $primary-color;
  color: $text-color-white;
  border: none;
  border-radius: $border-radius-base;
  padding: 0 $spacing-base;
  font-size: $font-size-sm;
}

.approvers-list {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.approver-tag {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  background: $primary-color;
  color: $text-color-white;
  border-radius: 8rpx;
  font-size: $font-size-xs;
}

.department-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.department-item {
  display: flex;
  align-items: center;
  padding: $spacing-base;
  border-bottom: 1rpx solid $border-color-light;
  font-size: $font-size-base;
  color: $text-color-primary;

  &:last-child {
    border-bottom: none;
  }

  &.all-departments {
    font-weight: 500;
    color: $primary-color;
  }
}

.department-divider {
  height: 1rpx;
  background: $border-color;
  margin: $spacing-sm 0;
}

.modal-footer {
  display: flex;
  gap: $spacing-base;
  padding: $spacing-lg;
  border-top: 1rpx solid $border-color-light;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  
  &.cancel-btn {
    background: $bg-color-light;
    color: $text-color-secondary;
  }
  
  &.confirm-btn {
    background: $primary-color;
    color: $text-color-white;
    
    &:disabled {
      opacity: 0.5;
    }
  }
}

/* 修复picker在modal中的层级问题 */
.modal-overlay {
  z-index: 999 !important;
}

.modal-content {
  z-index: 1000 !important;
  position: relative;
}

.modal-content picker {
  z-index: 1002 !important;
  position: relative;
}

.modal-content .picker-input {
  position: relative;
  z-index: 1002 !important;
}

/* 全局picker层级修复 */
:deep(.uni-picker-container) {
  z-index: 1003 !important;
}

:deep(.uni-picker) {
  z-index: 1003 !important;
}

/* 节点移动动画 */
.approval-node {
  transition: all 0.3s ease;
}

/* 用户选择样式 */
.users-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: $spacing-base;
  border-bottom: 2rpx solid $border-color;
  cursor: pointer;
  transition: background-color $transition-fast;
}

.user-item:hover {
  background: $bg-color-light;
}

.user-item:active {
  background: $bg-color-hover;
}

.user-item checkbox {
  margin-right: $spacing-base;
}
</style>
