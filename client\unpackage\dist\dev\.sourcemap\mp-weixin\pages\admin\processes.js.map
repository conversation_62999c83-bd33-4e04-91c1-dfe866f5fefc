{"version": 3, "file": "processes.js", "sources": ["pages/admin/processes.vue", "D:/HBuilderX.3.4.18.20220630/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWRtaW4vcHJvY2Vzc2VzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"processes-container\">\n    <!-- 搜索栏 -->\n    <view class=\"search-section\">\n      <view class=\"search-wrapper\">\n        <input \n          class=\"search-input\" \n          type=\"text\" \n          placeholder=\"搜索流程名称...\"\n          v-model=\"searchKeyword\"\n          @input=\"handleSearch\"\n        />\n        <Icon name=\"search\" size=\"32\" color=\"#999\" />\n      </view>\n    </view>\n\n    <!-- 流程列表 -->\n    <view class=\"list-section\">\n      <scroll-view \n        scroll-y \n        class=\"scroll-view\"\n        refresher-enabled\n        @refresherrefresh=\"onRefresh\"\n        :refresher-triggered=\"refreshing\"\n      >\n        <view v-if=\"processList.length > 0\">\n          <view \n            v-for=\"process in filteredProcesses\" \n            :key=\"process.id\"\n            class=\"process-card\"\n          >\n            <view class=\"process-info\">\n              <view class=\"process-details\">\n                <text class=\"process-name\">{{ process.name }}</text>\n                <text class=\"process-meta\">\n                  节点数量：{{ getNodeCount(process) }}个\n                </text>\n                <text class=\"process-meta\">\n                  创建时间：{{ formatDate(process.created_at) }}\n                </text>\n                <text class=\"process-meta\" v-if=\"process.ticket_count !== undefined\">\n                  使用次数：{{ process.ticket_count }}次\n                </text>\n              </view>\n            </view>\n            \n            <view class=\"process-actions\">\n              <button class=\"action-btn design-btn\" @click=\"designProcess(process)\">\n                设计\n              </button>\n              <button class=\"action-btn edit-btn\" @click=\"editProcess(process)\">\n                编辑\n              </button>\n              <button\n                class=\"action-btn delete-btn\"\n                @click=\"deleteProcess(process)\"\n                :disabled=\"process.ticket_count > 0\"\n              >\n                删除\n              </button>\n            </view>\n          </view>\n        </view>\n\n        <!-- 空状态 -->\n        <view v-else-if=\"!loading\" class=\"empty-state\">\n          <text class=\"empty-text\">暂无流程数据</text>\n          <button class=\"add-first-btn\" @click=\"showAddModal\">创建第一个流程</button>\n        </view>\n\n        <!-- 加载状态 -->\n        <view v-if=\"loading\" class=\"loading-state\">\n          <text>加载中...</text>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 浮动添加按钮 -->\n    <view class=\"floating-btn\" @click=\"showAddModal\" v-if=\"processList.length > 0\">\n      <text class=\"btn-icon\">+</text>\n    </view>\n\n    <!-- 添加/编辑流程弹窗 -->\n    <view class=\"modal-overlay\" v-if=\"showModal\" @click=\"hideModal\">\n      <view class=\"modal-content\" @click.stop>\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">{{ isEdit ? '编辑流程' : '创建流程' }}</text>\n          <text class=\"modal-close\" @click=\"hideModal\">×</text>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"form-item\">\n            <view class=\"form-label\">流程名称</view>\n            <input \n              class=\"form-input\" \n              type=\"text\" \n              placeholder=\"请输入流程名称\"\n              v-model=\"processForm.name\"\n              maxlength=\"50\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <view class=\"form-label\">流程描述</view>\n            <textarea \n              class=\"form-textarea\" \n              placeholder=\"请输入流程描述（可选）\"\n              v-model=\"processForm.description\"\n              maxlength=\"200\"\n            />\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <button class=\"modal-btn cancel-btn\" @click=\"hideModal\">取消</button>\n          <button\n            class=\"modal-btn confirm-btn\"\n            @click=\"saveProcess\"\n            :disabled=\"saveLoading || !processForm.name.trim()\"\n          >\n            <text v-if=\"saveLoading\">保存中...</text>\n            <text v-else>{{ isEdit ? '保存' : '创建' }}</text>\n          </button>\n          <!-- 临时测试按钮 -->\n          <button class=\"modal-btn\" @click=\"testClick\" style=\"background: orange; color: white;\">测试</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      loading: false,\n      refreshing: false,\n      saveLoading: false,\n      searchKeyword: '',\n      showModal: false,\n      isEdit: false,\n      \n      processList: [],\n      \n      processForm: {\n        id: '',\n        name: '',\n        description: ''\n      }\n    }\n  },\n  \n  computed: {\n    // 过滤后的流程列表\n    filteredProcesses() {\n      if (!this.searchKeyword.trim()) {\n        return this.processList\n      }\n      \n      return this.processList.filter(process => \n        process.name.toLowerCase().includes(this.searchKeyword.toLowerCase())\n      )\n    }\n  },\n  \n  onLoad() {\n    console.log('页面加载完成')\n    console.log('API对象:', this.$api)\n    console.log('process API:', this.$api.process)\n    this.loadProcesses()\n  },\n  \n  onShow() {\n    // 页面显示时刷新数据\n    this.loadProcesses()\n  },\n  \n  methods: {\n    // 获取节点数量\n    getNodeCount(process) {\n      if (process.nodes && Array.isArray(process.nodes)) {\n        return process.nodes.length\n      }\n      return 0\n    },\n\n    // 加载流程列表\n    async loadProcesses() {\n      this.loading = true\n\n      try {\n        const result = await this.$api.process.list()\n        console.log('流程API返回:', result)\n        if (result.code === 200) {\n          // 统一数据格式，确保包含所有必要字段\n          let processList = []\n          if (result.data && result.data.list) {\n            processList = result.data.list\n          } else if (Array.isArray(result.data)) {\n            processList = result.data\n          } else {\n            processList = []\n          }\n\n          // 确保每个流程都有必要的字段\n          this.processList = processList.map(process => ({\n            ...process,\n            ticket_count: process.ticket_count || 0,\n            nodes: process.nodes || [],\n            description: process.description || ''\n          }))\n\n          console.log('格式化后的流程列表:', this.processList)\n        } else {\n          console.error('API返回错误:', result)\n          uni.showToast({\n            title: result.message || '加载失败',\n            icon: 'none',\n            duration: 3000\n          })\n        }\n      } catch (error) {\n        console.error('加载流程列表失败:', error)\n        let errorMessage = '加载失败，请重试'\n        if (error.message && error.message !== 'undefined') {\n          errorMessage = error.message\n        }\n        uni.showToast({\n          title: errorMessage,\n          icon: 'none',\n          duration: 3000\n        })\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 搜索处理\n    handleSearch() {\n      // 实时搜索，无需额外处理\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.refreshing = true\n      this.loadProcesses()\n    },\n    \n    // 显示添加弹窗\n    showAddModal() {\n      this.isEdit = false\n      this.processForm = {\n        id: '',\n        name: '',\n        description: ''\n      }\n      this.showModal = true\n    },\n    \n    // 编辑流程基本信息\n    editProcess(process) {\n      this.isEdit = true\n      this.processForm = {\n        id: process.id,\n        name: process.name,\n        description: process.description || ''\n      }\n      this.showModal = true\n    },\n    \n    // 设计流程\n    designProcess(process) {\n      uni.navigateTo({\n        url: `/pages/admin/process-design?id=${process.id}&name=${encodeURIComponent(process.name)}`\n      })\n    },\n    \n    // 隐藏弹窗\n    hideModal() {\n      this.showModal = false\n      this.processForm = {\n        id: '',\n        name: '',\n        description: ''\n      }\n    },\n    \n    // 测试方法\n    testClick() {\n      console.log('测试点击事件正常工作')\n      uni.showToast({\n        title: '点击事件正常',\n        icon: 'success'\n      })\n    },\n\n    // 保存流程\n    async saveProcess() {\n      console.log('saveProcess 方法被调用')\n      console.log('表单数据:', this.processForm)\n      console.log('是否编辑模式:', this.isEdit)\n\n      if (!this.processForm.name.trim()) {\n        uni.showToast({\n          title: '请输入流程名称',\n          icon: 'none'\n        })\n        return\n      }\n\n      this.saveLoading = true\n      console.log('开始保存，saveLoading:', this.saveLoading)\n\n      try {\n        let result\n        const requestData = {\n          name: this.processForm.name.trim(),\n          description: this.processForm.description.trim()\n        }\n        console.log('请求数据:', requestData)\n\n        if (this.isEdit) {\n          console.log('执行更新操作，ID:', this.processForm.id)\n          result = await this.$api.process.update(this.processForm.id, requestData)\n        } else {\n          console.log('执行创建操作')\n          result = await this.$api.process.create(requestData)\n        }\n\n        console.log('API返回结果:', result)\n\n        if (result.code === 200) {\n          uni.showToast({\n            title: this.isEdit ? '修改成功' : '创建成功',\n            icon: 'success'\n          })\n\n          this.hideModal()\n          this.loadProcesses()\n\n          // 如果是新创建的流程，直接跳转到设计页面\n          if (!this.isEdit && result.data && result.data.id) {\n            setTimeout(() => {\n              uni.navigateTo({\n                url: `/pages/admin/process-design?id=${result.data.id}&name=${encodeURIComponent(result.data.name)}`\n              })\n            }, 1500)\n          }\n        } else {\n          console.error('API返回错误:', result)\n          uni.showToast({\n            title: result.message || '操作失败',\n            icon: 'none',\n            duration: 3000\n          })\n        }\n      } catch (error) {\n        console.error('保存流程失败:', error)\n        let errorMessage = '操作失败，请重试'\n        if (error.message && error.message !== 'undefined') {\n          errorMessage = error.message\n        }\n        uni.showToast({\n          title: errorMessage,\n          icon: 'none',\n          duration: 3000\n        })\n      } finally {\n        this.saveLoading = false\n        console.log('保存完成，saveLoading:', this.saveLoading)\n      }\n    },\n    \n    // 删除流程\n    deleteProcess(process) {\n      if (process.ticket_count > 0) {\n        uni.showToast({\n          title: `该流程已被使用${process.ticket_count}次，无法删除`,\n          icon: 'none'\n        })\n        return\n      }\n      \n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除流程\"${process.name}\"吗？删除后无法恢复。`,\n        confirmText: '删除',\n        confirmColor: '#e74c3c',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const result = await this.$api.process.delete(process.id)\n              \n              if (result.code === 200) {\n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'success'\n                })\n                this.loadProcesses()\n              } else {\n                uni.showToast({\n                  title: result.message || '删除失败',\n                  icon: 'none'\n                })\n              }\n            } catch (error) {\n              console.error('删除流程失败:', error)\n              let errorMessage = '删除失败，请重试'\n              if (error.message && error.message !== 'undefined') {\n                errorMessage = error.message\n              }\n              uni.showToast({\n                title: errorMessage,\n                icon: 'none',\n                duration: 3000\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    // 格式化日期\n    formatDate(dateStr) {\n      if (!dateStr) return ''\n      const date = new Date(dateStr)\n      return date.toLocaleDateString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.processes-container {\n  min-height: 100vh;\n  background: $bg-color-page;\n  padding-bottom: 120rpx;\n}\n\n.search-section {\n  background: $bg-color-white;\n  padding: $spacing-lg;\n  margin-bottom: $spacing-base;\n  box-shadow: $box-shadow-light;\n}\n\n.search-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  flex: 1;\n  height: 80rpx;\n  border: 2rpx solid $border-color;\n  border-radius: 40rpx;\n  padding: 0 $spacing-lg 0 $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n  \n  &:focus {\n    border-color: $primary-color;\n  }\n}\n\n.search-icon {\n  position: absolute;\n  right: $spacing-base;\n  font-size: $font-size-lg;\n  color: $text-color-secondary;\n}\n\n.list-section {\n  flex: 1;\n}\n\n.scroll-view {\n  height: calc(100vh - 200rpx);\n}\n\n.process-card {\n  background: $bg-color-white;\n  margin: 0 $spacing-lg $spacing-base;\n  padding: $spacing-lg;\n  border-radius: $border-radius-base;\n  box-shadow: $box-shadow-light;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.process-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.process-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: $border-radius-base;\n  background: linear-gradient(135deg, $warning-color, #ff9f43);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: $font-size-lg;\n  margin-right: $spacing-base;\n}\n\n.process-details {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.process-name {\n  font-size: $font-size-lg;\n  font-weight: bold;\n  color: $text-color-primary;\n  margin-bottom: $spacing-xs;\n}\n\n.process-meta {\n  font-size: $font-size-sm;\n  color: $text-color-secondary;\n  margin-bottom: 4rpx;\n}\n\n.process-actions {\n  display: flex;\n  gap: $spacing-sm;\n}\n\n.action-btn {\n  padding: $spacing-sm $spacing-base;\n  border: none;\n  border-radius: $border-radius-sm;\n  font-size: $font-size-sm;\n  \n  &.design-btn {\n    background: $primary-color;\n    color: $text-color-white;\n  }\n  \n  &.edit-btn {\n    background: $warning-color;\n    color: $text-color-white;\n  }\n  \n  &.delete-btn {\n    background: $danger-color;\n    color: $text-color-white;\n    \n    &:disabled {\n      opacity: 0.5;\n    }\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 100rpx $spacing-lg;\n}\n\n.empty-text {\n  display: block;\n  font-size: $font-size-base;\n  color: $text-color-secondary;\n  margin-bottom: $spacing-lg;\n}\n\n.add-first-btn {\n  background: $primary-color;\n  color: $text-color-white;\n  border: none;\n  border-radius: $border-radius-base;\n  padding: $spacing-base $spacing-lg;\n  font-size: $font-size-base;\n}\n\n.loading-state {\n  text-align: center;\n  padding: $spacing-lg;\n  color: $text-color-secondary;\n  font-size: $font-size-sm;\n}\n\n.floating-btn {\n  position: fixed;\n  bottom: 160rpx;\n  right: $spacing-lg;\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 50%;\n  background: $primary-color;\n  color: $text-color-white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 40rpx rgba(52, 152, 219, 0.3);\n  z-index: 100;\n}\n\n.btn-icon {\n  font-size: 60rpx;\n  font-weight: 300;\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: $bg-color-white;\n  border-radius: $border-radius-lg;\n  width: 600rpx;\n  max-width: 90vw;\n  overflow: hidden;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: $spacing-lg;\n  border-bottom: 1rpx solid $border-color-light;\n}\n\n.modal-title {\n  font-size: $font-size-lg;\n  font-weight: bold;\n  color: $text-color-primary;\n}\n\n.modal-close {\n  font-size: $font-size-xxl;\n  color: $text-color-secondary;\n  line-height: 1;\n}\n\n.modal-body {\n  padding: $spacing-lg;\n}\n\n.form-item {\n  margin-bottom: $spacing-lg;\n}\n\n.form-label {\n  font-size: $font-size-base;\n  color: $text-color-primary;\n  margin-bottom: $spacing-sm;\n  font-weight: 500;\n}\n\n.form-input {\n  width: 100%;\n  height: 88rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: 0 $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n  \n  &:focus {\n    border-color: $primary-color;\n  }\n}\n\n.form-textarea {\n  width: 100%;\n  min-height: 120rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n  line-height: 1.5;\n  \n  &:focus {\n    border-color: $primary-color;\n  }\n}\n\n.modal-footer {\n  display: flex;\n  gap: $spacing-base;\n  padding: $spacing-lg;\n  border-top: 1rpx solid $border-color-light;\n}\n\n.modal-btn {\n  flex: 1;\n  height: 80rpx;\n  border: none;\n  border-radius: $border-radius-base;\n  font-size: $font-size-base;\n  \n  &.cancel-btn {\n    background: $bg-color-light;\n    color: $text-color-secondary;\n  }\n  \n  &.confirm-btn {\n    background: $primary-color;\n    color: $text-color-white;\n    \n    &:disabled {\n      opacity: 0.5;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/code/ssl_gongdan/client/pages/admin/processes.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAoIA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,eAAe;AAAA,MACf,WAAW;AAAA,MACX,QAAQ;AAAA,MAER,aAAa,CAAE;AAAA,MAEf,aAAa;AAAA,QACX,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,oBAAoB;AAClB,UAAI,CAAC,KAAK,cAAc,QAAQ;AAC9B,eAAO,KAAK;AAAA,MACd;AAEA,aAAO,KAAK,YAAY;AAAA,QAAO,aAC7B,QAAQ,KAAK,YAAa,EAAC,SAAS,KAAK,cAAc,aAAa;AAAA,MACtE;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AACPA,kBAAAA,MAAA,MAAA,OAAA,oCAAY,QAAQ;AACpBA,kBAAA,MAAA,MAAA,OAAA,oCAAY,UAAU,KAAK,IAAI;AAC/BA,kBAAY,MAAA,MAAA,OAAA,oCAAA,gBAAgB,KAAK,KAAK,OAAO;AAC7C,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,SAAS;AAEP,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,aAAa,SAAS;AACpB,UAAI,QAAQ,SAAS,MAAM,QAAQ,QAAQ,KAAK,GAAG;AACjD,eAAO,QAAQ,MAAM;AAAA,MACvB;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,KAAK,QAAQ,KAAK;AAC5CA,sBAAAA,MAAY,MAAA,OAAA,oCAAA,YAAY,MAAM;AAC9B,YAAI,OAAO,SAAS,KAAK;AAEvB,cAAI,cAAc,CAAC;AACnB,cAAI,OAAO,QAAQ,OAAO,KAAK,MAAM;AACnC,0BAAc,OAAO,KAAK;AAAA,UAC5B,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AACrC,0BAAc,OAAO;AAAA,iBAChB;AACL,0BAAc,CAAC;AAAA,UACjB;AAGA,eAAK,cAAc,YAAY,IAAI,cAAY;AAAA,YAC7C,GAAG;AAAA,YACH,cAAc,QAAQ,gBAAgB;AAAA,YACtC,OAAO,QAAQ,SAAS,CAAE;AAAA,YAC1B,aAAa,QAAQ,eAAe;AAAA,UACtC,EAAE;AAEFA,wBAAY,MAAA,MAAA,OAAA,oCAAA,cAAc,KAAK,WAAW;AAAA,eACrC;AACLA,wBAAAA,MAAA,MAAA,SAAA,oCAAc,YAAY,MAAM;AAChCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,oCAAA,aAAa,KAAK;AAChC,YAAI,eAAe;AACnB,YAAI,MAAM,WAAW,MAAM,YAAY,aAAa;AAClD,yBAAe,MAAM;AAAA,QACvB;AACAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH,UAAU;AACR,aAAK,UAAU;AACf,aAAK,aAAa;AAAA,MACpB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AAAA,IAEd;AAAA;AAAA,IAGD,YAAY;AACV,WAAK,aAAa;AAClB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,SAAS;AACd,WAAK,cAAc;AAAA,QACjB,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AACA,WAAK,YAAY;AAAA,IAClB;AAAA;AAAA,IAGD,YAAY,SAAS;AACnB,WAAK,SAAS;AACd,WAAK,cAAc;AAAA,QACjB,IAAI,QAAQ;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,aAAa,QAAQ,eAAe;AAAA,MACtC;AACA,WAAK,YAAY;AAAA,IAClB;AAAA;AAAA,IAGD,cAAc,SAAS;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kCAAkC,QAAQ,EAAE,SAAS,mBAAmB,QAAQ,IAAI,CAAC;AAAA,OAC3F;AAAA,IACF;AAAA;AAAA,IAGD,YAAY;AACV,WAAK,YAAY;AACjB,WAAK,cAAc;AAAA,QACjB,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACVA,oBAAAA,MAAA,MAAA,OAAA,oCAAY,YAAY;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,cAAc;AAClBA,oBAAAA,uDAAY,mBAAmB;AAC/BA,oBAAA,MAAA,MAAA,OAAA,oCAAY,SAAS,KAAK,WAAW;AACrCA,oBAAA,MAAA,MAAA,OAAA,oCAAY,WAAW,KAAK,MAAM;AAElC,UAAI,CAAC,KAAK,YAAY,KAAK,KAAI,GAAI;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,WAAK,cAAc;AACnBA,oBAAA,MAAA,MAAA,OAAA,oCAAY,qBAAqB,KAAK,WAAW;AAEjD,UAAI;AACF,YAAI;AACJ,cAAM,cAAc;AAAA,UAClB,MAAM,KAAK,YAAY,KAAK,KAAM;AAAA,UAClC,aAAa,KAAK,YAAY,YAAY,KAAK;AAAA,QACjD;AACAA,sBAAAA,MAAA,MAAA,OAAA,oCAAY,SAAS,WAAW;AAEhC,YAAI,KAAK,QAAQ;AACfA,+EAAY,cAAc,KAAK,YAAY,EAAE;AAC7C,mBAAS,MAAM,KAAK,KAAK,QAAQ,OAAO,KAAK,YAAY,IAAI,WAAW;AAAA,eACnE;AACLA,wBAAAA,MAAY,MAAA,OAAA,oCAAA,QAAQ;AACpB,mBAAS,MAAM,KAAK,KAAK,QAAQ,OAAO,WAAW;AAAA,QACrD;AAEAA,sBAAAA,MAAY,MAAA,OAAA,oCAAA,YAAY,MAAM;AAE9B,YAAI,OAAO,SAAS,KAAK;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,KAAK,SAAS,SAAS;AAAA,YAC9B,MAAM;AAAA,WACP;AAED,eAAK,UAAU;AACf,eAAK,cAAc;AAGnB,cAAI,CAAC,KAAK,UAAU,OAAO,QAAQ,OAAO,KAAK,IAAI;AACjD,uBAAW,MAAM;AACfA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK,kCAAkC,OAAO,KAAK,EAAE,SAAS,mBAAmB,OAAO,KAAK,IAAI,CAAC;AAAA,eACnG;AAAA,YACF,GAAE,IAAI;AAAA,UACT;AAAA,eACK;AACLA,wBAAAA,MAAA,MAAA,SAAA,oCAAc,YAAY,MAAM;AAChCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,WAAW,KAAK;AAC9B,YAAI,eAAe;AACnB,YAAI,MAAM,WAAW,MAAM,YAAY,aAAa;AAClD,yBAAe,MAAM;AAAA,QACvB;AACAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH,UAAU;AACR,aAAK,cAAc;AACnBA,6EAAY,qBAAqB,KAAK,WAAW;AAAA,MACnD;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,SAAS;AACrB,UAAI,QAAQ,eAAe,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,UAAU,QAAQ,YAAY;AAAA,UACrC,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,WAAW,QAAQ,IAAI;AAAA,QAChC,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACF,oBAAM,SAAS,MAAM,KAAK,KAAK,QAAQ,OAAO,QAAQ,EAAE;AAExD,kBAAI,OAAO,SAAS,KAAK;AACvBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,iBACP;AACD,qBAAK,cAAc;AAAA,qBACd;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO,OAAO,WAAW;AAAA,kBACzB,MAAM;AAAA,iBACP;AAAA,cACH;AAAA,YACA,SAAO,OAAO;AACdA,4BAAAA,MAAc,MAAA,SAAA,oCAAA,WAAW,KAAK;AAC9B,kBAAI,eAAe;AACnB,kBAAI,MAAM,WAAW,MAAM,YAAY,aAAa;AAClD,+BAAe,MAAM;AAAA,cACvB;AACAA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,eACX;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,aAAO,KAAK,mBAAmB,OAAO;AAAA,IACxC;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7aA,GAAG,WAAW,eAAe;"}