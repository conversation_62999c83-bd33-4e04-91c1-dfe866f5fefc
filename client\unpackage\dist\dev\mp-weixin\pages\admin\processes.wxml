<view class="processes-container data-v-923b5701"><view class="search-section data-v-923b5701"><view class="search-wrapper data-v-923b5701"><input class="search-input data-v-923b5701" type="text" placeholder="搜索流程名称..." bindinput="{{a}}" value="{{b}}"/><icon wx:if="{{c}}" class="data-v-923b5701" u-i="923b5701-0" bind:__l="__l" u-p="{{c}}"/></view></view><view class="list-section data-v-923b5701"><scroll-view scroll-y class="scroll-view data-v-923b5701" refresher-enabled bindrefresherrefresh="{{i}}" refresher-triggered="{{j}}"><view wx:if="{{d}}" class="data-v-923b5701"><view wx:for="{{e}}" wx:for-item="process" wx:key="j" class="process-card data-v-923b5701"><view class="process-info data-v-923b5701"><view class="process-details data-v-923b5701"><text class="process-name data-v-923b5701">{{process.a}}</text><text class="process-meta data-v-923b5701"> 节点数量：{{process.b}}个 </text><text class="process-meta data-v-923b5701"> 创建时间：{{process.c}}</text><text wx:if="{{process.d}}" class="process-meta data-v-923b5701"> 使用次数：{{process.e}}次 </text></view></view><view class="process-actions data-v-923b5701"><button class="action-btn design-btn data-v-923b5701" bindtap="{{process.f}}"> 设计 </button><button class="action-btn edit-btn data-v-923b5701" bindtap="{{process.g}}"> 编辑 </button><button class="action-btn delete-btn data-v-923b5701" bindtap="{{process.h}}" disabled="{{process.i}}"> 删除 </button></view></view></view><view wx:elif="{{f}}" class="empty-state data-v-923b5701"><text class="empty-text data-v-923b5701">暂无流程数据</text><button class="add-first-btn data-v-923b5701" bindtap="{{g}}">创建第一个流程</button></view><view wx:if="{{h}}" class="loading-state data-v-923b5701"><text class="data-v-923b5701">加载中...</text></view></scroll-view></view><view wx:if="{{k}}" class="floating-btn data-v-923b5701" bindtap="{{l}}"><text class="btn-icon data-v-923b5701">+</text></view><view wx:if="{{m}}" class="modal-overlay data-v-923b5701" bindtap="{{B}}"><view class="modal-content data-v-923b5701" catchtap="{{A}}"><view class="modal-header data-v-923b5701"><text class="modal-title data-v-923b5701">{{n}}</text><text class="modal-close data-v-923b5701" bindtap="{{o}}">×</text></view><view class="modal-body data-v-923b5701"><view class="form-item data-v-923b5701"><view class="form-label data-v-923b5701">流程名称</view><input class="form-input data-v-923b5701" type="text" placeholder="请输入流程名称" maxlength="50" value="{{p}}" bindinput="{{q}}"/></view><view class="form-item data-v-923b5701"><view class="form-label data-v-923b5701">流程描述</view><block wx:if="{{r0}}"><textarea class="form-textarea data-v-923b5701" placeholder="请输入流程描述（可选）" maxlength="200" value="{{r}}" bindinput="{{s}}"/></block></view></view><view class="modal-footer data-v-923b5701"><button class="modal-btn cancel-btn data-v-923b5701" bindtap="{{t}}">取消</button><button class="modal-btn confirm-btn data-v-923b5701" bindtap="{{x}}" disabled="{{y}}"><text wx:if="{{v}}" class="data-v-923b5701">保存中...</text><text wx:else class="data-v-923b5701">{{w}}</text></button><button class="modal-btn data-v-923b5701" bindtap="{{z}}" style="background:orange;color:white">测试</button></view></view></view></view>