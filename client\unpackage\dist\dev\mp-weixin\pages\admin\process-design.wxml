<view class="process-design-container data-v-7074fd25"><view class="header-section data-v-7074fd25"><view class="process-info data-v-7074fd25"><text class="process-name data-v-7074fd25">{{a}}</text><text wx:if="{{b}}" class="process-desc data-v-7074fd25">{{c}}</text><text wx:else class="process-desc data-v-7074fd25">流程设计</text></view><button class="save-btn data-v-7074fd25" bindtap="{{e}}" disabled="{{f}}"><text wx:if="{{d}}" class="data-v-7074fd25">保存中...</text><text wx:else class="data-v-7074fd25">保存</text></button></view><view class="nodes-section data-v-7074fd25"><view class="section-title data-v-7074fd25">流程节点</view><view class="nodes-list data-v-7074fd25"><view class="node-item submit-node data-v-7074fd25"><view class="node-header data-v-7074fd25"><view class="node-icon data-v-7074fd25"><icon wx:if="{{g}}" class="data-v-7074fd25" u-i="7074fd25-0" bind:__l="__l" u-p="{{g}}"/></view><view class="node-info data-v-7074fd25"><text class="node-title data-v-7074fd25">提交节点</text><text class="node-desc data-v-7074fd25">工单提交入口</text></view><view class="node-status data-v-7074fd25">固定</view></view><view class="node-config data-v-7074fd25"><view class="config-item data-v-7074fd25"><text class="config-label data-v-7074fd25">可提交部门：</text><view class="department-tags data-v-7074fd25"><view wx:for="{{h}}" wx:for-item="dept" wx:key="c" class="department-tag data-v-7074fd25">{{dept.a}} <text class="tag-remove data-v-7074fd25" bindtap="{{dept.b}}">×</text></view><button class="add-department-btn data-v-7074fd25" bindtap="{{i}}"> + 添加部门 </button></view></view></view></view><view wx:for="{{j}}" wx:for-item="node" wx:key="q" class="node-item approval-node data-v-7074fd25"><view class="node-header data-v-7074fd25"><view class="node-icon data-v-7074fd25"><icon wx:if="{{k}}" class="data-v-7074fd25" u-i="{{node.a}}" bind:__l="__l" u-p="{{k}}"/></view><view class="node-info data-v-7074fd25"><text class="node-title data-v-7074fd25">{{node.b}}</text><text class="node-desc data-v-7074fd25">{{node.c}}</text></view><view class="node-actions data-v-7074fd25"><view class="move-buttons data-v-7074fd25"><button class="move-btn up-btn data-v-7074fd25" bindtap="{{node.d}}" disabled="{{node.e}}">↑</button><button class="move-btn down-btn data-v-7074fd25" bindtap="{{node.f}}" disabled="{{node.g}}">↓</button></view><button class="node-btn edit-btn data-v-7074fd25" bindtap="{{node.h}}">编辑</button><button class="node-btn delete-btn data-v-7074fd25" bindtap="{{node.i}}">删除</button></view></view><view class="node-config data-v-7074fd25"><view class="config-item data-v-7074fd25"><text class="config-label data-v-7074fd25">审批方式：</text><text class="config-value data-v-7074fd25">{{node.j}}</text></view><view wx:if="{{node.k}}" class="config-item data-v-7074fd25"><text class="config-label data-v-7074fd25">审批部门：</text><text class="config-value data-v-7074fd25">{{node.l}}</text></view><view wx:if="{{node.m}}" class="config-item data-v-7074fd25"><text class="config-label data-v-7074fd25">审批人员：</text><text class="config-value data-v-7074fd25">{{node.n}}</text></view><view class="config-item data-v-7074fd25"><text class="config-label data-v-7074fd25">允许转单：</text><text class="config-value data-v-7074fd25">{{node.o}}</text></view><view class="config-item data-v-7074fd25"><text class="config-label data-v-7074fd25">允许驳回：</text><text class="config-value data-v-7074fd25">{{node.p}}</text></view></view></view><view class="add-node-section data-v-7074fd25"><button class="add-node-btn data-v-7074fd25" bindtap="{{l}}"> + 添加审批节点 </button></view></view></view><view class="preview-section data-v-7074fd25"><view class="section-title data-v-7074fd25">流程预览</view><view class="flow-preview data-v-7074fd25"><view class="flow-step data-v-7074fd25"><view class="step-icon submit data-v-7074fd25"><icon wx:if="{{m}}" class="data-v-7074fd25" u-i="7074fd25-2" bind:__l="__l" u-p="{{m}}"/></view><text class="step-text data-v-7074fd25">提交工单</text></view><view wx:for="{{n}}" wx:for-item="node" wx:key="c" class="flow-step data-v-7074fd25"><view class="step-arrow data-v-7074fd25">↓</view><view class="step-icon approval data-v-7074fd25"><icon wx:if="{{o}}" class="data-v-7074fd25" u-i="{{node.a}}" bind:__l="__l" u-p="{{o}}"/></view><text class="step-text data-v-7074fd25">{{node.b}}</text></view><view class="flow-step data-v-7074fd25"><view class="step-arrow data-v-7074fd25">↓</view><view class="step-icon complete data-v-7074fd25"><icon wx:if="{{p}}" class="data-v-7074fd25" u-i="7074fd25-4" bind:__l="__l" u-p="{{p}}"/></view><text class="step-text data-v-7074fd25">完成</text></view></view></view><view wx:if="{{q}}" class="modal-overlay data-v-7074fd25" bindtap="{{V}}"><view class="modal-content data-v-7074fd25" catchtap="{{U}}"><view class="modal-header data-v-7074fd25"><text class="modal-title data-v-7074fd25">{{r}}</text><text class="modal-close data-v-7074fd25" bindtap="{{s}}">×</text></view><view class="modal-body data-v-7074fd25"><view class="form-item data-v-7074fd25"><view class="form-label data-v-7074fd25">节点名称</view><input class="form-input data-v-7074fd25" type="text" placeholder="请输入节点名称" maxlength="20" value="{{t}}" bindinput="{{v}}"/></view><view class="form-item data-v-7074fd25"><view class="form-label data-v-7074fd25">审批类型</view><view class="radio-group data-v-7074fd25"><label class="radio-item data-v-7074fd25" bindtap="{{x}}"><radio class="data-v-7074fd25" value="department" checked="{{w}}"/><text class="data-v-7074fd25">部门审批</text></label><label class="radio-item data-v-7074fd25" bindtap="{{z}}"><radio class="data-v-7074fd25" value="person" checked="{{y}}"/><text class="data-v-7074fd25">个人审批</text></label></view></view><view wx:if="{{A}}" class="form-item data-v-7074fd25"><view class="form-label data-v-7074fd25">选择部门</view><picker class="data-v-7074fd25" mode="selector" range="{{D}}" range-key="name" bindchange="{{E}}" value="{{F}}"><view class="picker-input data-v-7074fd25"><text wx:if="{{B}}" class="data-v-7074fd25">{{C}}</text><text wx:else class="placeholder data-v-7074fd25">请选择部门</text><text class="picker-arrow data-v-7074fd25">></text></view></picker></view><view wx:if="{{G}}" class="form-item data-v-7074fd25"><view class="form-label data-v-7074fd25">选择审批人员</view><view class="picker-input data-v-7074fd25" bindtap="{{J}}"><text wx:if="{{H}}" class="data-v-7074fd25">已选择 {{I}} 人</text><text wx:else class="placeholder data-v-7074fd25">请选择审批人员</text><text class="picker-arrow data-v-7074fd25">></text></view><view wx:if="{{K}}" class="selected-users data-v-7074fd25"><text class="selected-label data-v-7074fd25">已选择：</text><view wx:for="{{L}}" wx:for-item="userId" wx:key="c" class="user-tag data-v-7074fd25">{{userId.a}} <text class="tag-remove data-v-7074fd25" bindtap="{{userId.b}}">×</text></view></view></view><view class="form-item data-v-7074fd25"><view class="form-label data-v-7074fd25">节点设置</view><view class="checkbox-group data-v-7074fd25"><label class="checkbox-item data-v-7074fd25"><checkbox class="data-v-7074fd25" checked="{{M}}" bindchange="{{N}}"/><text class="data-v-7074fd25">允许转单</text></label><label class="checkbox-item data-v-7074fd25"><checkbox class="data-v-7074fd25" checked="{{O}}" bindchange="{{P}}"/><text class="data-v-7074fd25">允许驳回</text></label></view></view></view><view class="modal-footer data-v-7074fd25"><button class="modal-btn cancel-btn data-v-7074fd25" bindtap="{{Q}}">取消</button><button class="modal-btn confirm-btn data-v-7074fd25" bindtap="{{S}}" disabled="{{T}}">{{R}}</button></view></view></view><view wx:if="{{W}}" class="modal-overlay data-v-7074fd25" bindtap="{{ad}}"><view class="modal-content data-v-7074fd25" catchtap="{{ac}}"><view class="modal-header data-v-7074fd25"><text class="modal-title data-v-7074fd25">选择可提交部门</text><text class="modal-close data-v-7074fd25" bindtap="{{X}}">×</text></view><view class="modal-body data-v-7074fd25"><view class="department-list data-v-7074fd25"><label class="department-item all-departments data-v-7074fd25"><checkbox class="data-v-7074fd25" value="all" checked="{{Y}}" bindchange="{{Z}}"/><text class="data-v-7074fd25">全部部门</text></label><view class="department-divider data-v-7074fd25"></view><label wx:for="{{aa}}" wx:for-item="dept" wx:key="d" class="department-item data-v-7074fd25" bindtap="{{dept.e}}"><checkbox class="data-v-7074fd25" value="{{dept.a}}" checked="{{dept.b}}"/><text class="data-v-7074fd25">{{dept.c}}</text></label></view></view><view class="modal-footer data-v-7074fd25"><button class="modal-btn confirm-btn data-v-7074fd25" bindtap="{{ab}}">确定</button></view></view></view></view><view wx:if="{{ae}}" class="modal-overlay data-v-7074fd25" bindtap="{{aj}}"><view class="modal-content data-v-7074fd25" catchtap="{{ai}}"><view class="modal-header data-v-7074fd25"><text class="modal-title data-v-7074fd25">选择审批人员</text><text class="modal-close data-v-7074fd25" bindtap="{{af}}">×</text></view><view class="modal-body data-v-7074fd25"><view class="users-list data-v-7074fd25"><label wx:for="{{ag}}" wx:for-item="user" wx:key="e" class="user-item data-v-7074fd25" bindtap="{{user.f}}"><checkbox class="data-v-7074fd25" value="{{user.a}}" checked="{{user.b}}"/><text class="data-v-7074fd25">{{user.c}}-{{user.d}}</text></label></view></view><view class="modal-footer data-v-7074fd25"><button class="modal-btn confirm-btn data-v-7074fd25" bindtap="{{ah}}">确定</button></view></view></view>