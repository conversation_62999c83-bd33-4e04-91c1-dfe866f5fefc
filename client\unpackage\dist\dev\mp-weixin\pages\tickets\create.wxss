/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ========== 事事了工单系统 - 自定义样式变量 ========== */
/* 主题色彩 */
/* 文字颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 状态颜色 */
/* 优先级颜色 */
/* 字体大小 */
/* 间距 */
/* 圆角 */
/* 阴影 */
/* 动画时间 */
/* 层级 */
.create-ticket-container.data-v-c0494a44 {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}
.form-section.data-v-c0494a44 {
  padding: 40rpx;
}
.form-wrapper.data-v-c0494a44 {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.form-item.data-v-c0494a44 {
  margin-bottom: 40rpx;
}
.form-item.data-v-c0494a44:last-child {
  margin-bottom: 0;
}
.form-label.data-v-c0494a44 {
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 20rpx;
  font-weight: 500;
}
.form-label.required.data-v-c0494a44::after {
  content: "*";
  color: #e74c3c;
  margin-left: 4rpx;
}
.form-input.data-v-c0494a44 {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  background: #ffffff;
}
.form-input.data-v-c0494a44:focus {
  border-color: #3498db;
}
.form-textarea.data-v-c0494a44 {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  padding: 30rpx;
  font-size: 28rpx;
  background: #ffffff;
  line-height: 1.5;
}
.form-textarea.data-v-c0494a44:focus {
  border-color: #3498db;
}
.form-editor.data-v-c0494a44 {
  width: 100%;
  min-height: 300rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  background: #ffffff;
}
.picker-input.data-v-c0494a44 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  background: #ffffff;
}
.picker-input .placeholder.data-v-c0494a44 {
  color: #bdc3c7;
}
.picker-input .picker-arrow.data-v-c0494a44 {
  color: #7f8c8d;
  font-size: 32rpx;
}
.priority-options.data-v-c0494a44 {
  display: flex;
  gap: 30rpx;
}
.priority-option.data-v-c0494a44 {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 30rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  background: #ffffff;
}
.priority-option.active.data-v-c0494a44 {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}
.priority-dot.data-v-c0494a44 {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.priority-dot.priority-low.data-v-c0494a44 {
  background: #95a5a6;
}
.priority-dot.priority-medium.data-v-c0494a44 {
  background: #f39c12;
}
.priority-dot.priority-high.data-v-c0494a44 {
  background: #e74c3c;
}
.priority-text.data-v-c0494a44 {
  font-size: 28rpx;
  color: #2c3e50;
}
.process-description.data-v-c0494a44 {
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: #ecf0f1;
  border-radius: 16rpx;
  border-left: 6rpx solid #3498db;
}
.description-text.data-v-c0494a44 {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.5;
}
.process-preview.data-v-c0494a44 {
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  padding: 30rpx;
  background: #ecf0f1;
}
.empty-process.data-v-c0494a44 {
  text-align: center;
  color: #7f8c8d;
  font-size: 24rpx;
  padding: 40rpx;
}
.process-node.data-v-c0494a44 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.process-node.data-v-c0494a44:last-child {
  margin-bottom: 0;
}
.node-icon.data-v-c0494a44 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #3498db;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 30rpx;
}
.node-info.data-v-c0494a44 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.node-title.data-v-c0494a44 {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 4rpx;
}
.node-desc.data-v-c0494a44 {
  font-size: 24rpx;
  color: #7f8c8d;
}
.node-arrow.data-v-c0494a44 {
  font-size: 32rpx;
  color: #7f8c8d;
  margin-left: 30rpx;
}
.action-section.data-v-c0494a44 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 40rpx;
  border-top: 1rpx solid #e0e6ed;
  display: flex;
  gap: 30rpx;
}
.cancel-btn.data-v-c0494a44 {
  flex: 1;
  height: 88rpx;
  background: #ffffff;
  color: #7f8c8d;
  border: 2rpx solid #e0e6ed;
  border-radius: 16rpx;
  font-size: 28rpx;
}
.submit-btn.data-v-c0494a44 {
  flex: 2;
  height: 88rpx;
  background: #3498db;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.submit-btn.data-v-c0494a44:disabled {
  opacity: 0.6;
}