[2025-09-20T08:08:10+08:00][sql] CONNECT:[ UseTime:0.001200s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:08:10+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000958s ]
[2025-09-20T08:08:10+08:00][sql] SELECT * FROM `users` WHERE (  `phone` = '13800138000'  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000314s ]
[2025-09-20T08:08:10+08:00][sql] UPDATE `users`  SET `last_login_at` = '1758326890' , `updated_at` = '2025-09-20 08:08:10.943921'  WHERE (  (  `id` = 1 ) ) AND `users`.`is_deleted` = '0' [ RunTime:0.000784s ]
[2025-09-20T08:08:10+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000678s ]
[2025-09-20T08:08:10+08:00][sql] SELECT * FROM `departments` WHERE (  `id` = 1 ) AND `departments`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000170s ]
[2025-09-20T08:08:11+08:00][sql] CONNECT:[ UseTime:0.023561s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:08:11+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000727s ]
[2025-09-20T08:08:11+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000276s ]
[2025-09-20T08:08:11+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000789s ]
[2025-09-20T08:08:11+08:00][sql] SHOW FULL COLUMNS FROM `approvals` [ RunTime:0.000625s ]
[2025-09-20T08:08:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `is_deleted` = 0  AND (  `creator_id` = 1 OR `current_approver_id` = 1  OR (  EXISTS ( SELECT * FROM `approvals` WHERE  ( approvals.ticket_id = tickets.id )  AND `approver_id` = 1 ) ) ) ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000330s ]
[2025-09-20T08:08:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `creator_id` = 1  AND `is_deleted` = 0 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000227s ]
[2025-09-20T08:08:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `current_approver_id` = 1  AND `status` IN ('pending','processing')  AND `is_deleted` = 0 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000251s ]
[2025-09-20T08:08:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  (  `creator_id` = 1 OR `current_approver_id` = 1 )  AND `status` IN ('pending','processing')  AND `is_deleted` = 0 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000204s ]
[2025-09-20T08:08:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  (  `creator_id` = 1 OR (  EXISTS ( SELECT * FROM `approvals` WHERE  ( approvals.ticket_id = tickets.id )  AND `approver_id` = 1 ) ) )  AND `status` = 'completed'  AND `is_deleted` = 0 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000196s ]
[2025-09-20T08:08:11+08:00][sql] CONNECT:[ UseTime:0.000830s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:08:11+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000591s ]
[2025-09-20T08:08:11+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000221s ]
[2025-09-20T08:08:11+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000525s ]
[2025-09-20T08:08:11+08:00][sql] SHOW FULL COLUMNS FROM `approvals` [ RunTime:0.000602s ]
[2025-09-20T08:08:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `is_deleted` = 0  AND (  `creator_id` = 1 OR `current_approver_id` = 1  OR (  EXISTS ( SELECT * FROM `approvals` WHERE  ( approvals.ticket_id = tickets.id )  AND `approver_id` = 1 ) ) ) ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000338s ]
[2025-09-20T08:08:11+08:00][sql] SELECT * FROM `tickets` WHERE (  `is_deleted` = 0  AND (  `creator_id` = 1 OR `current_approver_id` = 1  OR (  EXISTS ( SELECT * FROM `approvals` WHERE  ( approvals.ticket_id = tickets.id )  AND `approver_id` = 1 ) ) ) ) AND `tickets`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000321s ]
[2025-09-20T08:08:11+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000676s ]
[2025-09-20T08:08:11+08:00][sql] SELECT * FROM `processes` WHERE (  (  `id` = 4 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000263s ]
[2025-09-20T08:08:11+08:00][sql] SELECT * FROM `users` WHERE (  (  `id` = 1 ) ) AND `users`.`is_deleted` = '0' [ RunTime:0.000296s ]
[2025-09-20T08:08:11+08:00][sql] SELECT * FROM `users` WHERE (  (  `id` = 1 ) ) AND `users`.`is_deleted` = '0' [ RunTime:0.000327s ]
[2025-09-20T08:08:12+08:00][sql] CONNECT:[ UseTime:0.000807s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:08:12+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000776s ]
[2025-09-20T08:08:12+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000312s ]
[2025-09-20T08:08:12+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000614s ]
[2025-09-20T08:08:12+08:00][sql] SHOW FULL COLUMNS FROM `approvals` [ RunTime:0.000600s ]
[2025-09-20T08:08:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `is_deleted` = 0  AND (  `creator_id` = 1 OR `current_approver_id` = 1  OR (  EXISTS ( SELECT * FROM `approvals` WHERE  ( approvals.ticket_id = tickets.id )  AND `approver_id` = 1 ) ) ) ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000316s ]
[2025-09-20T08:08:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `creator_id` = 1  AND `is_deleted` = 0 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000239s ]
[2025-09-20T08:08:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `current_approver_id` = 1  AND `status` IN ('pending','processing')  AND `is_deleted` = 0 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000233s ]
[2025-09-20T08:08:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  (  `creator_id` = 1 OR `current_approver_id` = 1 )  AND `status` IN ('pending','processing')  AND `is_deleted` = 0 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000333s ]
[2025-09-20T08:08:12+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  (  `creator_id` = 1 OR (  EXISTS ( SELECT * FROM `approvals` WHERE  ( approvals.ticket_id = tickets.id )  AND `approver_id` = 1 ) ) )  AND `status` = 'completed'  AND `is_deleted` = 0 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000292s ]
[2025-09-20T08:08:17+08:00][sql] CONNECT:[ UseTime:0.025482s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:08:17+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000852s ]
[2025-09-20T08:08:17+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000278s ]
[2025-09-20T08:08:17+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000899s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000291s ]
[2025-09-20T08:08:17+08:00][sql] SELECT * FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000357s ]
[2025-09-20T08:08:17+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000967s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 6 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000234s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 5 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000160s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000143s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 3 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000128s ]
[2025-09-20T08:08:17+08:00][sql] CONNECT:[ UseTime:0.016990s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:08:17+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000775s ]
[2025-09-20T08:08:17+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000284s ]
[2025-09-20T08:08:17+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000933s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000349s ]
[2025-09-20T08:08:17+08:00][sql] SELECT * FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000274s ]
[2025-09-20T08:08:17+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000846s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 6 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000245s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 5 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000350s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000166s ]
[2025-09-20T08:08:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 3 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000131s ]
[2025-09-20T08:09:17+08:00][sql] CONNECT:[ UseTime:0.001044s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:09:17+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.001024s ]
[2025-09-20T08:09:17+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000329s ]
[2025-09-20T08:09:17+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000774s ]
[2025-09-20T08:09:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000278s ]
[2025-09-20T08:09:17+08:00][sql] SELECT * FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000215s ]
[2025-09-20T08:09:17+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000928s ]
[2025-09-20T08:09:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 6 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000244s ]
[2025-09-20T08:09:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 5 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000165s ]
[2025-09-20T08:09:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000145s ]
[2025-09-20T08:09:17+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 3 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000129s ]
[2025-09-20T08:09:20+08:00][sql] CONNECT:[ UseTime:0.024067s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:09:20+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.001047s ]
[2025-09-20T08:09:20+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000328s ]
[2025-09-20T08:09:20+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000639s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000264s ]
[2025-09-20T08:09:20+08:00][sql] SELECT * FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000290s ]
[2025-09-20T08:09:20+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000828s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 6 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000314s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 5 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000220s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000197s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 3 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000252s ]
[2025-09-20T08:09:20+08:00][sql] CONNECT:[ UseTime:0.000923s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:09:20+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000822s ]
[2025-09-20T08:09:20+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000309s ]
[2025-09-20T08:09:20+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000595s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000345s ]
[2025-09-20T08:09:20+08:00][sql] SELECT * FROM `processes` WHERE (  `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000284s ]
[2025-09-20T08:09:20+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000771s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 6 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000313s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 5 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000369s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000240s ]
[2025-09-20T08:09:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 3 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000175s ]
[2025-09-20T08:09:21+08:00][sql] CONNECT:[ UseTime:0.000778s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:09:21+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000802s ]
[2025-09-20T08:09:21+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000323s ]
[2025-09-20T08:09:21+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000736s ]
[2025-09-20T08:09:21+08:00][sql] SELECT COUNT(*) AS think_count FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000255s ]
[2025-09-20T08:09:21+08:00][sql] SELECT * FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' ORDER BY `id` ASC LIMIT 0,15 [ RunTime:0.000221s ]
[2025-09-20T08:09:21+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 1  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000463s ]
[2025-09-20T08:09:21+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 2  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000189s ]
[2025-09-20T08:09:21+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 3  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000289s ]
[2025-09-20T08:09:21+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 4  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000209s ]
[2025-09-20T08:09:21+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 5  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000152s ]
[2025-09-20T08:09:21+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 6  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000287s ]
[2025-09-20T08:09:21+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 7  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000212s ]
[2025-09-20T08:09:22+08:00][sql] CONNECT:[ UseTime:0.025122s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:09:22+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000796s ]
[2025-09-20T08:09:22+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000408s ]
[2025-09-20T08:09:22+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000398s ]
[2025-09-20T08:09:22+08:00][sql] SELECT * FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000207s ]
[2025-09-20T08:09:22+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000918s ]
[2025-09-20T08:09:22+08:00][sql] SELECT * FROM `departments` WHERE (  (  `id` = 1 ) ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000541s ]
[2025-09-20T08:09:22+08:00][sql] CONNECT:[ UseTime:0.000756s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:09:22+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000959s ]
[2025-09-20T08:09:22+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000356s ]
[2025-09-20T08:09:22+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000557s ]
[2025-09-20T08:09:22+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000245s ]
[2025-09-20T08:09:22+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000665s ]
[2025-09-20T08:09:22+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000300s ]
[2025-09-20T08:10:14+08:00][sql] CONNECT:[ UseTime:0.022923s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:10:14+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000978s ]
[2025-09-20T08:10:14+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.001696s ]
[2025-09-20T08:10:14+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000764s ]
[2025-09-20T08:10:14+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000205s ]
[2025-09-20T08:10:14+08:00][sql] UPDATE `processes`  SET `nodes` = '[{\"type\":\"submit\",\"name\":\"提交工单\",\"departments\":[1]},{\"type\":\"department\",\"name\":\"222\"},{\"type\":\"department\",\"name\":\"66\"}]' , `updated_at` = '2025-09-20 08:10:14.256989'  WHERE (  (  `id` = 4 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000535s ]
[2025-09-20T08:10:21+08:00][sql] CONNECT:[ UseTime:0.001158s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:10:21+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000796s ]
[2025-09-20T08:10:21+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000325s ]
[2025-09-20T08:10:21+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000797s ]
[2025-09-20T08:10:21+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000294s ]
[2025-09-20T08:10:21+08:00][sql] UPDATE `processes`  SET `nodes` = '[{\"type\":\"submit\",\"name\":\"提交工单\",\"departments\":[1]},{\"type\":\"department\",\"name\":\"222\"},{\"type\":\"department\",\"name\":\"66\"}]' , `updated_at` = '2025-09-20 08:10:21.853765'  WHERE (  (  `id` = 4 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.001349s ]
[2025-09-20T08:12:52+08:00][sql] CONNECT:[ UseTime:0.018912s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:12:52+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.001454s ]
[2025-09-20T08:12:52+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000399s ]
[2025-09-20T08:12:52+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000623s ]
[2025-09-20T08:12:52+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000191s ]
[2025-09-20T08:12:52+08:00][sql] UPDATE `processes`  SET `nodes` = '[{\"type\":\"submit\",\"name\":\"提交工单\",\"departments\":[1]},{\"type\":\"department\",\"name\":\"222\"},{\"type\":\"department\",\"name\":\"66\"}]' , `updated_at` = '2025-09-20 08:12:52.994532'  WHERE (  (  `id` = 4 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000729s ]
[2025-09-20T08:13:45+08:00][sql] CONNECT:[ UseTime:0.000981s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:13:45+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000915s ]
[2025-09-20T08:13:45+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000271s ]
[2025-09-20T08:13:45+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000713s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000242s ]
[2025-09-20T08:13:45+08:00][sql] SELECT * FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' ORDER BY `id` ASC LIMIT 0,15 [ RunTime:0.000177s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 1  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000455s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 2  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000184s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 3  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000146s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 4  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000137s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 5  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000366s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 6  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000212s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 7  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000232s ]
[2025-09-20T08:13:45+08:00][sql] CONNECT:[ UseTime:0.025080s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:13:45+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.001001s ]
[2025-09-20T08:13:45+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000312s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000291s ]
[2025-09-20T08:13:45+08:00][sql] SELECT * FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000254s ]
[2025-09-20T08:13:45+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000752s ]
[2025-09-20T08:13:45+08:00][sql] SELECT * FROM `departments` WHERE (  (  `id` = 1 ) ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000293s ]
[2025-09-20T08:13:45+08:00][sql] CONNECT:[ UseTime:0.020301s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:13:45+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.001002s ]
[2025-09-20T08:13:45+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000563s ]
[2025-09-20T08:13:45+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000901s ]
[2025-09-20T08:13:45+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000391s ]
[2025-09-20T08:13:45+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.001144s ]
[2025-09-20T08:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000461s ]
[2025-09-20T08:14:42+08:00][sql] CONNECT:[ UseTime:0.000908s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:14:42+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000847s ]
[2025-09-20T08:14:42+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000266s ]
[2025-09-20T08:14:42+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000761s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000344s ]
[2025-09-20T08:14:42+08:00][sql] SELECT * FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' ORDER BY `id` ASC LIMIT 0,15 [ RunTime:0.000286s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 1  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000665s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 2  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000395s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 3  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000310s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 4  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000263s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 5  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000366s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 6  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000311s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 7  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000303s ]
[2025-09-20T08:14:42+08:00][sql] CONNECT:[ UseTime:0.017496s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:14:42+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000827s ]
[2025-09-20T08:14:42+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000252s ]
[2025-09-20T08:14:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000287s ]
[2025-09-20T08:14:42+08:00][sql] SELECT * FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000202s ]
[2025-09-20T08:14:42+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000952s ]
[2025-09-20T08:14:42+08:00][sql] SELECT * FROM `departments` WHERE (  (  `id` = 1 ) ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000231s ]
[2025-09-20T08:14:43+08:00][sql] CONNECT:[ UseTime:0.001002s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:14:43+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000802s ]
[2025-09-20T08:14:43+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000385s ]
[2025-09-20T08:14:43+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.001159s ]
[2025-09-20T08:14:43+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000300s ]
[2025-09-20T08:14:43+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.001079s ]
[2025-09-20T08:14:43+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000305s ]
[2025-09-20T08:15:28+08:00][sql] CONNECT:[ UseTime:0.023291s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:15:28+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.001094s ]
[2025-09-20T08:15:28+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000342s ]
[2025-09-20T08:15:28+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000967s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000320s ]
[2025-09-20T08:15:28+08:00][sql] SELECT * FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' ORDER BY `id` ASC LIMIT 0,15 [ RunTime:0.000327s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 1  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000566s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 2  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000421s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 3  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000313s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 4  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000197s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 5  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000364s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 6  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000254s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 7  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000339s ]
[2025-09-20T08:15:28+08:00][sql] CONNECT:[ UseTime:0.000870s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:15:28+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.001200s ]
[2025-09-20T08:15:28+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000362s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000243s ]
[2025-09-20T08:15:28+08:00][sql] SELECT * FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000200s ]
[2025-09-20T08:15:28+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000703s ]
[2025-09-20T08:15:28+08:00][sql] SELECT * FROM `departments` WHERE (  (  `id` = 1 ) ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000276s ]
[2025-09-20T08:15:28+08:00][sql] CONNECT:[ UseTime:0.025180s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:15:28+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000945s ]
[2025-09-20T08:15:28+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000382s ]
[2025-09-20T08:15:28+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000600s ]
[2025-09-20T08:15:28+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000245s ]
[2025-09-20T08:15:28+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000585s ]
[2025-09-20T08:15:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000241s ]
[2025-09-20T08:16:54+08:00][sql] CONNECT:[ UseTime:0.013433s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000853s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `users` WHERE (  `phone` = '13800138000'  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000359s ]
[2025-09-20T08:16:54+08:00][sql] UPDATE `users`  SET `last_login_at` = '1758327414' , `updated_at` = '2025-09-20 08:16:54.696856'  WHERE (  (  `id` = 1 ) ) AND `users`.`is_deleted` = '0' [ RunTime:0.000680s ]
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000868s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `departments` WHERE (  `id` = 1 ) AND `departments`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000316s ]
[2025-09-20T08:16:54+08:00][sql] CONNECT:[ UseTime:0.020879s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000771s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000326s ]
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000762s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `processes` WHERE (  `name` = '测试流程设计修复1758327414'  AND `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000318s ]
[2025-09-20T08:16:54+08:00][sql] INSERT INTO `processes` SET `name` = '测试流程设计修复1758327414' , `description` = '用于测试流程设计修复的流程' , `nodes` = '[]' , `is_active` = 1 , `created_at` = '2025-09-20 08:16:54.775662' , `updated_at` = '2025-09-20 08:16:54.775667' [ RunTime:0.001169s ]
[2025-09-20T08:16:54+08:00][sql] CONNECT:[ UseTime:0.000721s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000791s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000341s ]
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000571s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 13 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000230s ]
[2025-09-20T08:16:54+08:00][sql] UPDATE `processes`  SET `nodes` = '[{\"type\":\"submit\",\"name\":\"提交工单\",\"departments\":[1]},{\"type\":\"approve\",\"name\":\"部门审批节点\",\"approve_type\":\"department\",\"department_id\":1},{\"type\":\"approve\",\"name\":\"个人审批节点\",\"approve_type\":\"user\",\"user_ids\":[1]}]' , `updated_at` = '2025-09-20 08:16:54.824869'  WHERE (  (  `id` = 13 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000258s ]
[2025-09-20T08:16:54+08:00][sql] CONNECT:[ UseTime:0.010221s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000645s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000213s ]
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000504s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 13 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000179s ]
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000507s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 13 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000280s ]
[2025-09-20T08:16:54+08:00][sql] CONNECT:[ UseTime:0.016525s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000733s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000211s ]
[2025-09-20T08:16:54+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000490s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000173s ]
[2025-09-20T08:16:54+08:00][sql] SELECT * FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' ORDER BY `id` ASC LIMIT 0,15 [ RunTime:0.000150s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 1  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000261s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 2  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000146s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 3  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000237s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 4  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000159s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 5  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000133s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 6  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000128s ]
[2025-09-20T08:16:54+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 7  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000121s ]
[2025-09-20T08:16:55+08:00][sql] CONNECT:[ UseTime:0.000948s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:16:55+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000735s ]
[2025-09-20T08:16:55+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000209s ]
[2025-09-20T08:16:55+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000230s ]
[2025-09-20T08:16:55+08:00][sql] SELECT * FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000163s ]
[2025-09-20T08:16:55+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000513s ]
[2025-09-20T08:16:55+08:00][sql] SELECT * FROM `departments` WHERE (  (  `id` = 1 ) ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000146s ]
[2025-09-20T08:16:55+08:00][sql] CONNECT:[ UseTime:0.020611s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:16:55+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000919s ]
[2025-09-20T08:16:55+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000288s ]
[2025-09-20T08:16:55+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000498s ]
[2025-09-20T08:16:55+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 13 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000175s ]
[2025-09-20T08:16:55+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000582s ]
[2025-09-20T08:16:55+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 13 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000220s ]
[2025-09-20T08:16:55+08:00][sql] UPDATE `processes`  SET `is_deleted` = 1 , `updated_at` = '2025-09-20 08:16:55.074823'  WHERE (  (  `id` = 13 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000193s ]
[2025-09-20T08:18:06+08:00][sql] CONNECT:[ UseTime:0.000931s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000820s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `users` WHERE (  `phone` = '13800138000'  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000378s ]
[2025-09-20T08:18:06+08:00][sql] UPDATE `users`  SET `last_login_at` = '1758327486' , `updated_at` = '2025-09-20 08:18:06.599894'  WHERE (  (  `id` = 1 ) ) AND `users`.`is_deleted` = '0' [ RunTime:0.001577s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000826s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `departments` WHERE (  `id` = 1 ) AND `departments`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000424s ]
[2025-09-20T08:18:06+08:00][sql] CONNECT:[ UseTime:0.000978s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000969s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000359s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000902s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `processes` WHERE (  `name` = '测试流程设计修复1758327486'  AND `is_deleted` = 0 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000206s ]
[2025-09-20T08:18:06+08:00][sql] INSERT INTO `processes` SET `name` = '测试流程设计修复1758327486' , `description` = '用于测试流程设计修复的流程' , `nodes` = '[]' , `is_active` = 1 , `created_at` = '2025-09-20 08:18:06.658780' , `updated_at` = '2025-09-20 08:18:06.658785' [ RunTime:0.000487s ]
[2025-09-20T08:18:06+08:00][sql] CONNECT:[ UseTime:0.000827s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000723s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000214s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000544s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 14 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000168s ]
[2025-09-20T08:18:06+08:00][sql] UPDATE `processes`  SET `nodes` = '[{\"type\":\"submit\",\"name\":\"提交工单\",\"departments\":[1]},{\"type\":\"approve\",\"name\":\"部门审批节点\",\"approve_type\":\"department\",\"department_id\":1},{\"type\":\"approve\",\"name\":\"个人审批节点\",\"approve_type\":\"user\",\"user_ids\":[1]}]' , `updated_at` = '2025-09-20 08:18:06.706687'  WHERE (  (  `id` = 14 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000375s ]
[2025-09-20T08:18:06+08:00][sql] CONNECT:[ UseTime:0.012472s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000663s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000209s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000496s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 14 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000164s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000507s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 14 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000180s ]
[2025-09-20T08:18:06+08:00][sql] CONNECT:[ UseTime:0.014584s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000819s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000327s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000614s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000383s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' ORDER BY `id` ASC LIMIT 0,15 [ RunTime:0.000262s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 1  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000321s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 2  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000222s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 3  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000226s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 4  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000217s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 5  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000218s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 6  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000226s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 7  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000413s ]
[2025-09-20T08:18:06+08:00][sql] CONNECT:[ UseTime:0.010501s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000751s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000315s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000345s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000400s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000569s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `departments` WHERE (  (  `id` = 1 ) ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000287s ]
[2025-09-20T08:18:06+08:00][sql] CONNECT:[ UseTime:0.013861s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000694s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000680s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000487s ]
[2025-09-20T08:18:06+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 14 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000168s ]
[2025-09-20T08:18:06+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000487s ]
[2025-09-20T08:18:06+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 14 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000187s ]
[2025-09-20T08:18:06+08:00][sql] UPDATE `processes`  SET `is_deleted` = 1 , `updated_at` = '2025-09-20 08:18:06.951516'  WHERE (  (  `id` = 14 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000175s ]
[2025-09-20T08:20:40+08:00][sql] CONNECT:[ UseTime:0.001848s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:20:40+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000779s ]
[2025-09-20T08:20:40+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000382s ]
[2025-09-20T08:20:40+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000665s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000270s ]
[2025-09-20T08:20:40+08:00][sql] SELECT * FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' ORDER BY `id` ASC LIMIT 0,15 [ RunTime:0.000228s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 1  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000328s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 2  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000206s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 3  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000183s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 4  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000182s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 5  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000160s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 6  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000136s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 7  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000133s ]
[2025-09-20T08:20:40+08:00][sql] CONNECT:[ UseTime:0.012024s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:20:40+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000831s ]
[2025-09-20T08:20:40+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000420s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000595s ]
[2025-09-20T08:20:40+08:00][sql] SELECT * FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000447s ]
[2025-09-20T08:20:40+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000648s ]
[2025-09-20T08:20:40+08:00][sql] SELECT * FROM `departments` WHERE (  (  `id` = 1 ) ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000234s ]
[2025-09-20T08:20:40+08:00][sql] CONNECT:[ UseTime:0.000771s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:20:40+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000810s ]
[2025-09-20T08:20:40+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000240s ]
[2025-09-20T08:20:40+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000760s ]
[2025-09-20T08:20:40+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000278s ]
[2025-09-20T08:20:40+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000758s ]
[2025-09-20T08:20:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000392s ]
[2025-09-20T08:21:26+08:00][sql] CONNECT:[ UseTime:0.001544s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:21:26+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.001015s ]
[2025-09-20T08:21:26+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000263s ]
[2025-09-20T08:21:26+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.001283s ]
[2025-09-20T08:21:26+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000257s ]
[2025-09-20T08:21:26+08:00][sql] UPDATE `processes`  SET `nodes` = '[{\"type\":\"submit\",\"name\":\"提交工单\",\"departments\":[1]},{\"type\":\"approve\",\"name\":\"222\",\"approve_type\":\"department\",\"department_id\":3},{\"type\":\"approve\",\"name\":\"66\"}]' , `updated_at` = '2025-09-20 08:21:26.886015'  WHERE (  (  `id` = 4 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000974s ]
[2025-09-20T08:21:35+08:00][sql] CONNECT:[ UseTime:0.000776s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:21:35+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000895s ]
[2025-09-20T08:21:35+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000354s ]
[2025-09-20T08:21:35+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000768s ]
[2025-09-20T08:21:35+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.001576s ]
[2025-09-20T08:21:35+08:00][sql] UPDATE `processes`  SET `nodes` = '[{\"type\":\"submit\",\"name\":\"提交工单\",\"departments\":[1]},{\"type\":\"approve\",\"name\":\"222\",\"approve_type\":\"department\",\"department_id\":3},{\"type\":\"approve\",\"name\":\"66\",\"approve_type\":\"department\",\"department_id\":2}]' , `updated_at` = '2025-09-20 08:21:35.160662'  WHERE (  (  `id` = 4 ) ) AND `processes`.`is_deleted` = '0' [ RunTime:0.000545s ]
[2025-09-20T08:21:37+08:00][sql] CONNECT:[ UseTime:0.000958s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:21:37+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000761s ]
[2025-09-20T08:21:37+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000235s ]
[2025-09-20T08:21:37+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000745s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000225s ]
[2025-09-20T08:21:37+08:00][sql] SELECT * FROM `departments` WHERE (  `is_deleted` = 0 ) AND `departments`.`is_deleted` = '0' ORDER BY `id` ASC LIMIT 0,15 [ RunTime:0.000312s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 1  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000428s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 2  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000174s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 3  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000142s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 4  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000134s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 5  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000169s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 6  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000188s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `department_id` = 7  AND `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000190s ]
[2025-09-20T08:21:37+08:00][sql] CONNECT:[ UseTime:0.012722s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:21:37+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000738s ]
[2025-09-20T08:21:37+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000290s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' [ RunTime:0.000278s ]
[2025-09-20T08:21:37+08:00][sql] SELECT * FROM `users` WHERE (  `is_deleted` = 0 ) AND `users`.`is_deleted` = '0' ORDER BY `id` DESC LIMIT 0,15 [ RunTime:0.000185s ]
[2025-09-20T08:21:37+08:00][sql] SHOW FULL COLUMNS FROM `departments` [ RunTime:0.000601s ]
[2025-09-20T08:21:37+08:00][sql] SELECT * FROM `departments` WHERE (  (  `id` = 1 ) ) AND `departments`.`is_deleted` = '0' [ RunTime:0.000165s ]
[2025-09-20T08:21:37+08:00][sql] CONNECT:[ UseTime:0.023951s ] mysql:host=127.0.0.1;port=3306;dbname=ssl_gongdan;charset=utf8mb4
[2025-09-20T08:21:37+08:00][sql] SHOW FULL COLUMNS FROM `users` [ RunTime:0.000914s ]
[2025-09-20T08:21:37+08:00][sql] SELECT * FROM `users` WHERE (  `id` = 1  AND `is_deleted` = 0  AND `is_active` = 1 ) AND `users`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000263s ]
[2025-09-20T08:21:37+08:00][sql] SHOW FULL COLUMNS FROM `processes` [ RunTime:0.000558s ]
[2025-09-20T08:21:37+08:00][sql] SELECT * FROM `processes` WHERE (  `id` = 4 ) AND `processes`.`is_deleted` = '0' LIMIT 1 [ RunTime:0.000181s ]
[2025-09-20T08:21:37+08:00][sql] SHOW FULL COLUMNS FROM `tickets` [ RunTime:0.000760s ]
[2025-09-20T08:21:37+08:00][sql] SELECT COUNT(*) AS think_count FROM `tickets` WHERE (  `process_id` = 4 ) AND `tickets`.`is_deleted` = '0' [ RunTime:0.000198s ]
