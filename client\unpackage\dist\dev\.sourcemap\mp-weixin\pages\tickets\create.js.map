{"version": 3, "file": "create.js", "sources": ["pages/tickets/create.vue", "D:/HBuilderX.3.4.18.20220630/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGlja2V0cy9jcmVhdGUudnVl"], "sourcesContent": ["<template>\n  <view class=\"create-ticket-container\">\n    <!-- 表单区域 -->\n    <view class=\"form-section\">\n      <view class=\"form-wrapper\">\n        <view class=\"form-item\">\n          <view class=\"form-label required\">工单标题</view>\n          <input \n            class=\"form-input\" \n            type=\"text\" \n            placeholder=\"请输入工单标题\"\n            v-model=\"ticketForm.title\"\n            maxlength=\"100\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <view class=\"form-label\">工单描述</view>\n          <!-- #ifdef MP-WEIXIN -->\n          <editor\n            id=\"description-editor\"\n            class=\"form-editor\"\n            placeholder=\"请详细描述工单内容...\"\n            :show-img-size=\"true\"\n            :show-img-toolbar=\"true\"\n            :show-img-resize=\"true\"\n            @ready=\"onEditorReady\"\n            @input=\"onEditorInput\"\n          />\n          <!-- #endif -->\n          <!-- #ifdef H5 -->\n          <textarea\n            class=\"form-textarea\"\n            placeholder=\"请详细描述工单内容...\"\n            v-model=\"ticketForm.description\"\n            maxlength=\"1000\"\n            :show-count=\"true\"\n          />\n          <!-- #endif -->\n        </view>\n        \n        <view class=\"form-item\">\n          <view class=\"form-label required\">工单流程</view>\n          <picker \n            mode=\"selector\" \n            :range=\"processList\" \n            range-key=\"name\"\n            @change=\"onProcessChange\"\n            :value=\"selectedProcessIndex\"\n          >\n            <view class=\"picker-input\">\n              <text v-if=\"ticketForm.processId\">{{ selectedProcessName }}</text>\n              <text v-else class=\"placeholder\">请选择工单流程</text>\n              <text class=\"picker-arrow\">></text>\n            </view>\n          </picker>\n        </view>\n        \n        <view class=\"form-item\">\n          <view class=\"form-label required\">优先级</view>\n          <view class=\"priority-options\">\n            <view \n              v-for=\"priority in priorityOptions\" \n              :key=\"priority.value\"\n              class=\"priority-option\"\n              :class=\"{ active: ticketForm.priority === priority.value }\"\n              @click=\"selectPriority(priority.value)\"\n            >\n              <view class=\"priority-dot\" :class=\"priority.class\"></view>\n              <text class=\"priority-text\">{{ priority.label }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-item\" v-if=\"selectedProcess\">\n          <view class=\"form-label\">流程预览</view>\n          <view v-if=\"selectedProcess.description\" class=\"process-description\">\n            <text class=\"description-text\">{{ selectedProcess.description }}</text>\n          </view>\n          <view class=\"process-preview\">\n            <view v-if=\"selectedProcess.nodes && selectedProcess.nodes.length > 0\">\n              <view\n                v-for=\"(node, index) in selectedProcess.nodes\"\n                :key=\"index\"\n                class=\"process-node\"\n              >\n                <view class=\"node-icon\">{{ index + 1 }}</view>\n                <view class=\"node-info\">\n                  <text class=\"node-title\">{{ node.name || '未命名节点' }}</text>\n                  <text class=\"node-desc\">{{ getNodeDescription(node) }}</text>\n                </view>\n                <view v-if=\"index < selectedProcess.nodes.length - 1\" class=\"node-arrow\">↓</view>\n              </view>\n            </view>\n            <view v-else class=\"empty-process\">\n              <text>该流程暂未配置审批节点</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部操作按钮 -->\n    <view class=\"action-section\">\n      <button class=\"cancel-btn\" @click=\"handleCancel\">取消</button>\n      <button class=\"submit-btn\" @click=\"handleSubmit\" :disabled=\"submitLoading\">\n        <text v-if=\"submitLoading\">提交中...</text>\n        <text v-else>提交工单</text>\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      submitLoading: false,\n      processList: [],\n      selectedProcessIndex: -1,\n      editorCtx: null,\n\n      ticketForm: {\n        title: '',\n        description: '',\n        processId: '',\n        priority: 'medium'\n      },\n      \n      priorityOptions: [\n        { value: 'low', label: '低优先级', class: 'priority-low' },\n        { value: 'medium', label: '中优先级', class: 'priority-medium' },\n        { value: 'high', label: '高优先级', class: 'priority-high' }\n      ]\n    }\n  },\n  \n  computed: {\n    selectedProcessName() {\n      if (this.selectedProcessIndex >= 0 &&\n          this.selectedProcessIndex < this.processList.length &&\n          this.processList[this.selectedProcessIndex]) {\n        return this.processList[this.selectedProcessIndex].name\n      }\n      return ''\n    },\n\n    selectedProcess() {\n      if (this.selectedProcessIndex >= 0 &&\n          this.selectedProcessIndex < this.processList.length &&\n          this.processList[this.selectedProcessIndex]) {\n        const process = this.processList[this.selectedProcessIndex]\n        // 确保返回的对象有nodes属性\n        return {\n          ...process,\n          nodes: process.nodes || []\n        }\n      }\n      return { nodes: [] }\n    }\n  },\n  \n  onLoad() {\n    this.loadProcessList()\n  },\n  \n  methods: {\n    // 加载流程列表\n    async loadProcessList() {\n      try {\n        const result = await this.$api.process.list()\n        console.log('流程API返回:', result)\n        if (result.code === 200) {\n          // 检查数据结构\n          if (result.data && result.data.list) {\n            this.processList = result.data.list\n          } else {\n            this.processList = result.data || []\n          }\n          console.log('流程列表:', this.processList)\n        }\n      } catch (error) {\n        console.error('加载流程列表失败:', error)\n        uni.showToast({\n          title: '加载流程失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 流程选择变化\n    onProcessChange(e) {\n      const index = e.detail.value\n      this.selectedProcessIndex = index\n\n      // 检查索引是否有效\n      if (index >= 0 && index < this.processList.length && this.processList[index]) {\n        this.ticketForm.processId = this.processList[index].id\n        console.log('选择的流程:', this.processList[index])\n      } else {\n        console.error('无效的流程索引:', index, '流程列表:', this.processList)\n        this.ticketForm.processId = ''\n      }\n    },\n    \n    // 选择优先级\n    selectPriority(priority) {\n      this.ticketForm.priority = priority\n    },\n\n    // 富文本编辑器就绪\n    onEditorReady() {\n      uni.createSelectorQuery().select('#description-editor').context((res) => {\n        this.editorCtx = res.context\n      }).exec()\n    },\n\n    // 富文本编辑器内容变化\n    onEditorInput(e) {\n      this.ticketForm.description = e.detail.html\n    },\n    \n    // 获取节点描述\n    getNodeDescription(node) {\n      if (node.type === 'submit') {\n        return '提交节点'\n      } else if (node.type === 'department') {\n        return `部门审批：${node.departmentName}`\n      } else if (node.type === 'person') {\n        return `个人审批：${node.approvers.join(', ')}`\n      }\n      return '未知节点'\n    },\n    \n    // 取消创建\n    handleCancel() {\n      uni.showModal({\n        title: '确认取消',\n        content: '确定要取消创建工单吗？已填写的内容将丢失。',\n        confirmText: '确定',\n        cancelText: '继续编辑',\n        success: (res) => {\n          if (res.confirm) {\n            uni.navigateBack()\n          }\n        }\n      })\n    },\n    \n    // 提交工单\n    async handleSubmit() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      // 检查流程权限\n      if (!this.checkProcessPermission()) {\n        return\n      }\n      \n      this.submitLoading = true\n      \n      try {\n        // 转换字段名以匹配后端期望\n        const submitData = {\n          title: this.ticketForm.title,\n          description: this.ticketForm.description,\n          process_id: this.ticketForm.processId, // 转换为后端期望的字段名\n          priority: this.ticketForm.priority\n        }\n\n        console.log('提交的工单数据:', submitData)\n        const result = await this.$api.ticket.create(submitData)\n        \n        if (result.code === 200) {\n          uni.showToast({\n            title: '工单创建成功',\n            icon: 'success'\n          })\n          \n          setTimeout(() => {\n            // 跳转到工单详情页\n            uni.redirectTo({\n              url: `/pages/tickets/detail?id=${result.data.id}`\n            })\n          }, 1500)\n        } else {\n          uni.showToast({\n            title: result.message || '创建失败',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('创建工单失败:', error)\n        uni.showToast({\n          title: '创建失败，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.submitLoading = false\n      }\n    },\n    \n    // 表单验证\n    validateForm() {\n      if (!this.ticketForm.title.trim()) {\n        uni.showToast({\n          title: '请输入工单标题',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.ticketForm.processId) {\n        uni.showToast({\n          title: '请选择工单流程',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.ticketForm.priority) {\n        uni.showToast({\n          title: '请选择优先级',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 检查流程权限\n    checkProcessPermission() {\n      if (!this.selectedProcess) {\n        return false\n      }\n      \n      const userInfo = uni.getStorageSync('userInfo')\n      if (!userInfo) {\n        return false\n      }\n      \n      // 检查用户是否在审批流程中\n      const nodes = this.selectedProcess.nodes || []\n      for (let node of nodes) {\n        if (node.type === 'department' && node.departmentId === userInfo.departmentId) {\n          uni.showToast({\n            title: '您在此审批流程中，无法发布工单',\n            icon: 'none'\n          })\n          return false\n        }\n        \n        if (node.type === 'person' && node.approvers.includes(userInfo.jobNumber)) {\n          uni.showToast({\n            title: '您在此审批流程中，无法发布工单',\n            icon: 'none'\n          })\n          return false\n        }\n      }\n      \n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.create-ticket-container {\n  min-height: 100vh;\n  background: $bg-color-page;\n  padding-bottom: 120rpx;\n}\n\n.form-section {\n  padding: $spacing-lg;\n}\n\n.form-wrapper {\n  background: $bg-color-white;\n  border-radius: $border-radius-base;\n  padding: $spacing-lg;\n  box-shadow: $box-shadow-light;\n}\n\n.form-item {\n  margin-bottom: $spacing-lg;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.form-label {\n  font-size: $font-size-base;\n  color: $text-color-primary;\n  margin-bottom: $spacing-sm;\n  font-weight: 500;\n  \n  &.required::after {\n    content: '*';\n    color: $danger-color;\n    margin-left: 4rpx;\n  }\n}\n\n.form-input {\n  width: 100%;\n  height: 88rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: 0 $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n  \n  &:focus {\n    border-color: $primary-color;\n  }\n}\n\n.form-textarea {\n  width: 100%;\n  min-height: 200rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n  line-height: 1.5;\n\n  &:focus {\n    border-color: $primary-color;\n  }\n}\n\n.form-editor {\n  width: 100%;\n  min-height: 300rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  background: $bg-color-white;\n}\n\n.picker-input {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: 0 $spacing-base;\n  font-size: $font-size-base;\n  background: $bg-color-white;\n  \n  .placeholder {\n    color: $text-color-placeholder;\n  }\n  \n  .picker-arrow {\n    color: $text-color-secondary;\n    font-size: $font-size-lg;\n  }\n}\n\n.priority-options {\n  display: flex;\n  gap: $spacing-base;\n}\n\n.priority-option {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  padding: $spacing-base;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  background: $bg-color-white;\n  \n  &.active {\n    border-color: $primary-color;\n    background: rgba(52, 152, 219, 0.1);\n  }\n}\n\n.priority-dot {\n  width: 20rpx;\n  height: 20rpx;\n  border-radius: 50%;\n  margin-right: $spacing-sm;\n  \n  &.priority-low {\n    background: $priority-low;\n  }\n  \n  &.priority-medium {\n    background: $priority-medium;\n  }\n  \n  &.priority-high {\n    background: $priority-high;\n  }\n}\n\n.priority-text {\n  font-size: $font-size-base;\n  color: $text-color-primary;\n}\n\n.process-description {\n  margin-bottom: $spacing-base;\n  padding: $spacing-base;\n  background: $bg-color-light;\n  border-radius: $border-radius-base;\n  border-left: 6rpx solid $primary-color;\n}\n\n.description-text {\n  font-size: $font-size-sm;\n  color: $text-color-secondary;\n  line-height: 1.5;\n}\n\n.process-preview {\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  padding: $spacing-base;\n  background: $bg-color-light;\n}\n\n.empty-process {\n  text-align: center;\n  color: $text-color-secondary;\n  font-size: $font-size-sm;\n  padding: $spacing-lg;\n}\n\n.process-node {\n  display: flex;\n  align-items: center;\n  margin-bottom: $spacing-base;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.node-icon {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  background: $primary-color;\n  color: $text-color-white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: $font-size-sm;\n  font-weight: bold;\n  margin-right: $spacing-base;\n}\n\n.node-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.node-title {\n  font-size: $font-size-base;\n  color: $text-color-primary;\n  font-weight: 500;\n  margin-bottom: 4rpx;\n}\n\n.node-desc {\n  font-size: $font-size-sm;\n  color: $text-color-secondary;\n}\n\n.node-arrow {\n  font-size: $font-size-lg;\n  color: $text-color-secondary;\n  margin-left: $spacing-base;\n}\n\n.action-section {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: $bg-color-white;\n  padding: $spacing-lg;\n  border-top: 1rpx solid $border-color;\n  display: flex;\n  gap: $spacing-base;\n}\n\n.cancel-btn {\n  flex: 1;\n  height: 88rpx;\n  background: $bg-color-white;\n  color: $text-color-secondary;\n  border: 2rpx solid $border-color;\n  border-radius: $border-radius-base;\n  font-size: $font-size-base;\n}\n\n.submit-btn {\n  flex: 2;\n  height: 88rpx;\n  background: $primary-color;\n  color: $text-color-white;\n  border: none;\n  border-radius: $border-radius-base;\n  font-size: $font-size-lg;\n  font-weight: bold;\n  \n  &:disabled {\n    opacity: 0.6;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/code/ssl_gongdan/client/pages/tickets/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAkHA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA,MACf,aAAa,CAAE;AAAA,MACf,sBAAsB;AAAA,MACtB,WAAW;AAAA,MAEX,YAAY;AAAA,QACV,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,MACX;AAAA,MAED,iBAAiB;AAAA,QACf,EAAE,OAAO,OAAO,OAAO,QAAQ,OAAO,eAAgB;AAAA,QACtD,EAAE,OAAO,UAAU,OAAO,QAAQ,OAAO,kBAAmB;AAAA,QAC5D,EAAE,OAAO,QAAQ,OAAO,QAAQ,OAAO,gBAAgB;AAAA,MACzD;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,sBAAsB;AACpB,UAAI,KAAK,wBAAwB,KAC7B,KAAK,uBAAuB,KAAK,YAAY,UAC7C,KAAK,YAAY,KAAK,oBAAoB,GAAG;AAC/C,eAAO,KAAK,YAAY,KAAK,oBAAoB,EAAE;AAAA,MACrD;AACA,aAAO;AAAA,IACR;AAAA,IAED,kBAAkB;AAChB,UAAI,KAAK,wBAAwB,KAC7B,KAAK,uBAAuB,KAAK,YAAY,UAC7C,KAAK,YAAY,KAAK,oBAAoB,GAAG;AAC/C,cAAM,UAAU,KAAK,YAAY,KAAK,oBAAoB;AAE1D,eAAO;AAAA,UACL,GAAG;AAAA,UACH,OAAO,QAAQ,SAAS,CAAC;AAAA,QAC3B;AAAA,MACF;AACA,aAAO,EAAE,OAAO,GAAG;AAAA,IACrB;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,gBAAgB;AAAA,EACtB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,kBAAkB;AACtB,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,KAAK,QAAQ,KAAK;AAC5CA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,YAAY,MAAM;AAC9B,YAAI,OAAO,SAAS,KAAK;AAEvB,cAAI,OAAO,QAAQ,OAAO,KAAK,MAAM;AACnC,iBAAK,cAAc,OAAO,KAAK;AAAA,iBAC1B;AACL,iBAAK,cAAc,OAAO,QAAQ,CAAC;AAAA,UACrC;AACAA,8EAAY,SAAS,KAAK,WAAW;AAAA,QACvC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB,GAAG;AACjB,YAAM,QAAQ,EAAE,OAAO;AACvB,WAAK,uBAAuB;AAG5B,UAAI,SAAS,KAAK,QAAQ,KAAK,YAAY,UAAU,KAAK,YAAY,KAAK,GAAG;AAC5E,aAAK,WAAW,YAAY,KAAK,YAAY,KAAK,EAAE;AACpDA,4BAAY,MAAA,OAAA,mCAAA,UAAU,KAAK,YAAY,KAAK,CAAC;AAAA,aACxC;AACLA,4BAAA,MAAA,SAAA,mCAAc,YAAY,OAAO,SAAS,KAAK,WAAW;AAC1D,aAAK,WAAW,YAAY;AAAA,MAC9B;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,WAAK,WAAW,WAAW;AAAA,IAC5B;AAAA;AAAA,IAGD,gBAAgB;AACdA,oBAAG,MAAC,oBAAmB,EAAG,OAAO,qBAAqB,EAAE,QAAQ,CAAC,QAAQ;AACvE,aAAK,YAAY,IAAI;AAAA,MACtB,CAAA,EAAE,KAAK;AAAA,IACT;AAAA;AAAA,IAGD,cAAc,GAAG;AACf,WAAK,WAAW,cAAc,EAAE,OAAO;AAAA,IACxC;AAAA;AAAA,IAGD,mBAAmB,MAAM;AACvB,UAAI,KAAK,SAAS,UAAU;AAC1B,eAAO;AAAA,MACT,WAAW,KAAK,SAAS,cAAc;AACrC,eAAO,QAAQ,KAAK,cAAc;AAAA,iBACzB,KAAK,SAAS,UAAU;AACjC,eAAO,QAAQ,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,aAAa;AAAA,UACnB;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe;AACnB,UAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,0BAA0B;AAClC;AAAA,MACF;AAEA,WAAK,gBAAgB;AAErB,UAAI;AAEF,cAAM,aAAa;AAAA,UACjB,OAAO,KAAK,WAAW;AAAA,UACvB,aAAa,KAAK,WAAW;AAAA,UAC7B,YAAY,KAAK,WAAW;AAAA;AAAA,UAC5B,UAAU,KAAK,WAAW;AAAA,QAC5B;AAEAA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,YAAY,UAAU;AAClC,cAAM,SAAS,MAAM,KAAK,KAAK,OAAO,OAAO,UAAU;AAEvD,YAAI,OAAO,SAAS,KAAK;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAED,qBAAW,MAAM;AAEfA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK,4BAA4B,OAAO,KAAK,EAAE;AAAA,aAChD;AAAA,UACF,GAAE,IAAI;AAAA,eACF;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,CAAC,KAAK,WAAW,MAAM,KAAI,GAAI;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,WAAW,WAAW;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,WAAW,UAAU;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,yBAAyB;AACvB,UAAI,CAAC,KAAK,iBAAiB;AACzB,eAAO;AAAA,MACT;AAEA,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AAGA,YAAM,QAAQ,KAAK,gBAAgB,SAAS,CAAC;AAC7C,eAAS,QAAQ,OAAO;AACtB,YAAI,KAAK,SAAS,gBAAgB,KAAK,iBAAiB,SAAS,cAAc;AAC7EA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,SAAS,YAAY,KAAK,UAAU,SAAS,SAAS,SAAS,GAAG;AACzEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7WA,GAAG,WAAW,eAAe;"}