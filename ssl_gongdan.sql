/*
 Navicat Premium Data Transfer

 Source Server         : 事事了工单
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26)
 Source Host           : localhost:3306
 Source Schema         : ssl_gongdan

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26)
 File Encoding         : 65001

 Date: 20/09/2025 08:24:06
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for approvals
-- ----------------------------
DROP TABLE IF EXISTS `approvals`;
CREATE TABLE `approvals`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审批记录ID',
  `ticket_id` int(11) UNSIGNED NOT NULL COMMENT '工单ID',
  `node_index` int(11) NOT NULL COMMENT '节点索引',
  `approver_id` int(11) UNSIGNED NOT NULL COMMENT '审批人ID',
  `action` enum('approve','reject','transfer') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '审批意见',
  `transferred_to` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '转单给谁(用户ID)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ticket_id`(`ticket_id`) USING BTREE,
  INDEX `idx_approver_id`(`approver_id`) USING BTREE,
  INDEX `idx_transferred_to`(`transferred_to`) USING BTREE,
  INDEX `idx_action`(`action`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_approvals_approver` FOREIGN KEY (`approver_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_approvals_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `tickets` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_approvals_transferred` FOREIGN KEY (`transferred_to`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审批记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of approvals
-- ----------------------------

-- ----------------------------
-- Table structure for comments
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `ticket_id` int(11) UNSIGNED NOT NULL COMMENT '工单ID',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '评论人ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评论内容',
  `images` json NULL COMMENT '图片URL数组(JSON格式)',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-否 1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ticket_id`(`ticket_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_comments_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `tickets` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of comments
-- ----------------------------

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门名称',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-否 1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of departments
-- ----------------------------
INSERT INTO `departments` VALUES (1, '管理员', 0, '2025-09-19 08:45:04', '2025-09-19 08:45:04');
INSERT INTO `departments` VALUES (2, '技术部', 0, '2025-09-19 08:45:04', '2025-09-19 08:45:04');
INSERT INTO `departments` VALUES (3, '产品部21', 0, '2025-09-19 08:45:04', '2025-09-19 14:27:25');
INSERT INTO `departments` VALUES (4, '运营部', 0, '2025-09-19 08:45:04', '2025-09-19 08:45:04');
INSERT INTO `departments` VALUES (5, '财务部', 0, '2025-09-19 08:45:04', '2025-09-19 08:45:04');
INSERT INTO `departments` VALUES (6, '人事部', 0, '2025-09-19 08:45:04', '2025-09-19 08:45:04');
INSERT INTO `departments` VALUES (7, '56', 0, '2025-09-19 13:52:32', '2025-09-19 13:52:32');

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '接收人ID',
  `ticket_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '相关工单ID',
  `type` enum('ticket_assigned','ticket_approved','ticket_rejected','ticket_transferred','ticket_completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知类型',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知内容',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读 0-否 1-是',
  `is_sent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已发送 0-否 1-是',
  `sent_at` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_ticket_id`(`ticket_id`) USING BTREE,
  INDEX `idx_type`(`type`) USING BTREE,
  INDEX `idx_is_read`(`is_read`) USING BTREE,
  INDEX `idx_is_sent`(`is_sent`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_notifications_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `tickets` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '消息通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of notifications
-- ----------------------------
INSERT INTO `notifications` VALUES (1, 1, NULL, 'ticket_assigned', '新工单待审批', '您有一个新的工单需要审批：???????', 0, 0, NULL, '2025-09-19 15:51:17');

-- ----------------------------
-- Table structure for processes
-- ----------------------------
DROP TABLE IF EXISTS `processes`;
CREATE TABLE `processes`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '流程ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '流程名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '流程描述',
  `nodes` json NOT NULL COMMENT '流程节点配置(JSON格式)',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用 0-否 1-是',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-否 1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '工单流程表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of processes
-- ----------------------------
INSERT INTO `processes` VALUES (1, '简单审批流程s', NULL, '[{\"name\": \"提交节点\", \"type\": \"submit\", \"departments\": [2, 3, 4, 5, 6], \"description\": \"可提交工单的部门\"}, {\"name\": \"部门审批\", \"type\": \"approve\", \"description\": \"管理员部门审批\", \"allow_reject\": true, \"approve_type\": \"department\", \"department_id\": 1, \"allow_transfer\": true}]', 1, 1, '2025-09-19 08:45:04', '2025-09-19 17:51:02');
INSERT INTO `processes` VALUES (2, '????', NULL, '[{\"name\": \"提交工单\", \"type\": \"submit\", \"departments\": [{\"id\": 1, \"name\": \"???\"}]}, {\"name\": \"????1\", \"type\": \"approve\", \"approve_type\": \"department\", \"department_id\": 2}]', 1, 1, '2025-09-19 14:05:58', '2025-09-19 17:02:08');
INSERT INTO `processes` VALUES (3, '测试1', '', '[]', 1, 0, '2025-09-19 14:47:46', '2025-09-19 17:51:05');
INSERT INTO `processes` VALUES (4, '测试2', NULL, '[{\"name\": \"提交工单\", \"type\": \"submit\", \"departments\": [1]}, {\"name\": \"222\", \"type\": \"approve\", \"approve_type\": \"department\", \"department_id\": 3}, {\"name\": \"66\", \"type\": \"approve\", \"approve_type\": \"department\", \"department_id\": 2}]', 1, 0, '2025-09-19 14:48:34', '2025-09-20 08:21:35');
INSERT INTO `processes` VALUES (5, '33', NULL, '[]', 1, 0, '2025-09-19 17:04:08', '2025-09-19 17:04:08');
INSERT INTO `processes` VALUES (6, 'wret', 'rgt', '[]', 1, 0, '2025-09-19 17:04:24', '2025-09-19 17:49:50');
INSERT INTO `processes` VALUES (7, '测试流程描述1758275007', '这是一个测试流程的描述信息', '[]', 1, 1, '2025-09-19 17:43:28', '2025-09-19 17:43:28');
INSERT INTO `processes` VALUES (8, '测试流程描述1758275169', '这是一个测试流程的描述信息', '[]', 1, 1, '2025-09-19 17:46:10', '2025-09-19 17:46:10');
INSERT INTO `processes` VALUES (9, '测试描述流程1758276218', '这是一个包含详细描述的测试流程，用于验证描述功能是否正常工作。', '[{\"name\": \"提交工单\", \"type\": \"submit\", \"departments\": [1]}, {\"name\": \"部门审批\", \"type\": \"approve\"}]', 1, 1, '2025-09-19 18:03:39', '2025-09-19 18:03:39');
INSERT INTO `processes` VALUES (10, '测试描述流程1758276247', '这是一个包含详细描述的测试流程，用于验证描述功能是否正常工作。', '[{\"name\": \"提交工单\", \"type\": \"submit\", \"departments\": [1]}, {\"name\": \"提交节点\", \"type\": \"submit\"}, {\"name\": \"部门审批\", \"type\": \"approve\"}]', 1, 1, '2025-09-19 18:04:08', '2025-09-19 18:04:08');
INSERT INTO `processes` VALUES (11, '测试描述流程1758276288', '这是一个包含详细描述的测试流程，用于验证描述功能是否正常工作。', '[{\"name\": \"提交工单\", \"type\": \"submit\", \"departments\": [1]}, {\"name\": \"提交节点\", \"type\": \"submit\"}, {\"name\": \"部门审批\", \"type\": \"approve\"}]', 1, 1, '2025-09-19 18:04:48', '2025-09-19 18:04:48');
INSERT INTO `processes` VALUES (12, '测试描述流程1758276349', '这是一个包含详细描述的测试流程，用于验证描述功能是否正常工作。', '[{\"name\": \"提交工单\", \"type\": \"submit\", \"departments\": [1]}, {\"name\": \"提交节点\", \"type\": \"submit\"}, {\"name\": \"部门审批\", \"type\": \"approve\", \"approve_type\": \"department\", \"department_id\": 1}]', 1, 1, '2025-09-19 18:05:50', '2025-09-19 18:05:50');
INSERT INTO `processes` VALUES (13, '测试流程设计修复1758327414', '用于测试流程设计修复的流程', '[{\"name\": \"提交工单\", \"type\": \"submit\", \"departments\": [1]}, {\"name\": \"部门审批节点\", \"type\": \"approve\", \"approve_type\": \"department\", \"department_id\": 1}, {\"name\": \"个人审批节点\", \"type\": \"approve\", \"user_ids\": [1], \"approve_type\": \"user\"}]', 1, 1, '2025-09-20 08:16:55', '2025-09-20 08:16:55');
INSERT INTO `processes` VALUES (14, '测试流程设计修复1758327486', '用于测试流程设计修复的流程', '[{\"name\": \"提交工单\", \"type\": \"submit\", \"departments\": [1]}, {\"name\": \"部门审批节点\", \"type\": \"approve\", \"approve_type\": \"department\", \"department_id\": 1}, {\"name\": \"个人审批节点\", \"type\": \"approve\", \"user_ids\": [1], \"approve_type\": \"user\"}]', 1, 1, '2025-09-20 08:18:07', '2025-09-20 08:18:07');

-- ----------------------------
-- Table structure for tickets
-- ----------------------------
DROP TABLE IF EXISTS `tickets`;
CREATE TABLE `tickets`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '工单ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工单标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '工单描述',
  `process_id` int(11) UNSIGNED NOT NULL COMMENT '流程ID',
  `priority` enum('low','medium','high') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium' COMMENT '优先级',
  `status` enum('pending','processing','completed','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态',
  `creator_id` int(11) UNSIGNED NOT NULL COMMENT '创建人ID',
  `current_node` int(11) NOT NULL DEFAULT 0 COMMENT '当前节点索引',
  `current_approver_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '当前审批人ID',
  `deadline` timestamp NULL DEFAULT NULL COMMENT '截止时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-否 1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_process_id`(`process_id`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_current_approver_id`(`current_approver_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_priority`(`priority`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_tickets_approver` FOREIGN KEY (`current_approver_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_tickets_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_tickets_process` FOREIGN KEY (`process_id`) REFERENCES `processes` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '工单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tickets
-- ----------------------------
INSERT INTO `tickets` VALUES (1, '??????', '????', 4, 'medium', 'completed', 1, 0, NULL, NULL, NULL, 0, '2025-09-19 15:34:26', '2025-09-19 15:34:26');
INSERT INTO `tickets` VALUES (2, '???????', '????', 4, 'medium', 'pending', 1, 1, 1, NULL, NULL, 0, '2025-09-19 15:51:17', '2025-09-19 15:51:17');

-- ----------------------------
-- Table structure for uploads
-- ----------------------------
DROP TABLE IF EXISTS `uploads`;
CREATE TABLE `uploads`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '上传记录ID',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '上传人ID',
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问URL',
  `file_size` int(11) UNSIGNED NOT NULL COMMENT '文件大小(字节)',
  `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MIME类型',
  `storage_type` enum('local','oss','cos') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'local' COMMENT '存储类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_uploads_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件上传记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of uploads
-- ----------------------------

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `job_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `department_id` int(11) UNSIGNED NOT NULL COMMENT '部门ID',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登录密码(H5用)',
  `openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信openid',
  `platform` enum('wechat','h5') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'wechat' COMMENT '注册平台',
  `is_admin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否管理员 0-否 1-是',
  `is_active` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否激活 0-否 1-是',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-否 1-是',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_job_number`(`job_number`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE,
  UNIQUE INDEX `uk_email`(`email`) USING BTREE,
  UNIQUE INDEX `uk_openid`(`openid`) USING BTREE,
  INDEX `idx_department_id`(`department_id`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  INDEX `idx_is_admin`(`is_admin`) USING BTREE,
  CONSTRAINT `fk_users_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'ADMIN001', 'users1', '13800138000', '<EMAIL>', 1, '$2y$10$6y1YjNgiBCuIDKDS/CFHQuvEsrkqcmh.3rPSTiLNxtrGZ2R4prC6W', NULL, 'h5', 1, 1, 0, '0000-00-00 00:00:00', '2025-09-19 08:45:04', '2025-09-20 08:18:07');

SET FOREIGN_KEY_CHECKS = 1;
