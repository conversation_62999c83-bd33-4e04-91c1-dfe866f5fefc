"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      loading: false,
      refreshing: false,
      saveLoading: false,
      searchKeyword: "",
      showModal: false,
      isEdit: false,
      processList: [],
      processForm: {
        id: "",
        name: "",
        description: ""
      }
    };
  },
  computed: {
    // 过滤后的流程列表
    filteredProcesses() {
      if (!this.searchKeyword.trim()) {
        return this.processList;
      }
      return this.processList.filter(
        (process) => process.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      );
    }
  },
  onLoad() {
    common_vendor.index.__f__("log", "at pages/admin/processes.vue:167", "页面加载完成");
    common_vendor.index.__f__("log", "at pages/admin/processes.vue:168", "API对象:", this.$api);
    common_vendor.index.__f__("log", "at pages/admin/processes.vue:169", "process API:", this.$api.process);
    this.loadProcesses();
  },
  onShow() {
    this.loadProcesses();
  },
  methods: {
    // 获取节点数量
    getNodeCount(process) {
      if (process.nodes && Array.isArray(process.nodes)) {
        return process.nodes.length;
      }
      return 0;
    },
    // 加载流程列表
    async loadProcesses() {
      this.loading = true;
      try {
        const result = await this.$api.process.list();
        common_vendor.index.__f__("log", "at pages/admin/processes.vue:193", "流程API返回:", result);
        if (result.code === 200) {
          let processList = [];
          if (result.data && result.data.list) {
            processList = result.data.list;
          } else if (Array.isArray(result.data)) {
            processList = result.data;
          } else {
            processList = [];
          }
          this.processList = processList.map((process) => ({
            ...process,
            ticket_count: process.ticket_count || 0,
            nodes: process.nodes || [],
            description: process.description || ""
          }));
          common_vendor.index.__f__("log", "at pages/admin/processes.vue:213", "格式化后的流程列表:", this.processList);
        } else {
          common_vendor.index.__f__("error", "at pages/admin/processes.vue:215", "API返回错误:", result);
          common_vendor.index.showToast({
            title: result.message || "加载失败",
            icon: "none",
            duration: 3e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/admin/processes.vue:223", "加载流程列表失败:", error);
        let errorMessage = "加载失败，请重试";
        if (error.message && error.message !== "undefined") {
          errorMessage = error.message;
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },
    // 搜索处理
    handleSearch() {
    },
    // 下拉刷新
    onRefresh() {
      this.refreshing = true;
      this.loadProcesses();
    },
    // 显示添加弹窗
    showAddModal() {
      this.isEdit = false;
      this.processForm = {
        id: "",
        name: "",
        description: ""
      };
      this.showModal = true;
    },
    // 编辑流程基本信息
    editProcess(process) {
      this.isEdit = true;
      this.processForm = {
        id: process.id,
        name: process.name,
        description: process.description || ""
      };
      this.showModal = true;
    },
    // 设计流程
    designProcess(process) {
      common_vendor.index.navigateTo({
        url: `/pages/admin/process-design?id=${process.id}&name=${encodeURIComponent(process.name)}`
      });
    },
    // 隐藏弹窗
    hideModal() {
      this.showModal = false;
      this.processForm = {
        id: "",
        name: "",
        description: ""
      };
    },
    // 测试方法
    testClick() {
      common_vendor.index.__f__("log", "at pages/admin/processes.vue:291", "测试点击事件正常工作");
      common_vendor.index.showToast({
        title: "点击事件正常",
        icon: "success"
      });
    },
    // 保存流程
    async saveProcess() {
      common_vendor.index.__f__("log", "at pages/admin/processes.vue:300", "saveProcess 方法被调用");
      common_vendor.index.__f__("log", "at pages/admin/processes.vue:301", "表单数据:", this.processForm);
      common_vendor.index.__f__("log", "at pages/admin/processes.vue:302", "是否编辑模式:", this.isEdit);
      if (!this.processForm.name.trim()) {
        common_vendor.index.showToast({
          title: "请输入流程名称",
          icon: "none"
        });
        return;
      }
      this.saveLoading = true;
      common_vendor.index.__f__("log", "at pages/admin/processes.vue:313", "开始保存，saveLoading:", this.saveLoading);
      try {
        let result;
        const requestData = {
          name: this.processForm.name.trim(),
          description: this.processForm.description.trim()
        };
        common_vendor.index.__f__("log", "at pages/admin/processes.vue:321", "请求数据:", requestData);
        if (this.isEdit) {
          common_vendor.index.__f__("log", "at pages/admin/processes.vue:324", "执行更新操作，ID:", this.processForm.id);
          result = await this.$api.process.update(this.processForm.id, requestData);
        } else {
          common_vendor.index.__f__("log", "at pages/admin/processes.vue:327", "执行创建操作");
          result = await this.$api.process.create(requestData);
        }
        common_vendor.index.__f__("log", "at pages/admin/processes.vue:331", "API返回结果:", result);
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: this.isEdit ? "修改成功" : "创建成功",
            icon: "success"
          });
          this.hideModal();
          this.loadProcesses();
          if (!this.isEdit && result.data && result.data.id) {
            setTimeout(() => {
              common_vendor.index.navigateTo({
                url: `/pages/admin/process-design?id=${result.data.id}&name=${encodeURIComponent(result.data.name)}`
              });
            }, 1500);
          }
        } else {
          common_vendor.index.__f__("error", "at pages/admin/processes.vue:351", "API返回错误:", result);
          common_vendor.index.showToast({
            title: result.message || "操作失败",
            icon: "none",
            duration: 3e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/admin/processes.vue:359", "保存流程失败:", error);
        let errorMessage = "操作失败，请重试";
        if (error.message && error.message !== "undefined") {
          errorMessage = error.message;
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
      } finally {
        this.saveLoading = false;
        common_vendor.index.__f__("log", "at pages/admin/processes.vue:371", "保存完成，saveLoading:", this.saveLoading);
      }
    },
    // 删除流程
    deleteProcess(process) {
      if (process.ticket_count > 0) {
        common_vendor.index.showToast({
          title: `该流程已被使用${process.ticket_count}次，无法删除`,
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除流程"${process.name}"吗？删除后无法恢复。`,
        confirmText: "删除",
        confirmColor: "#e74c3c",
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await this.$api.process.delete(process.id);
              if (result.code === 200) {
                common_vendor.index.showToast({
                  title: "删除成功",
                  icon: "success"
                });
                this.loadProcesses();
              } else {
                common_vendor.index.showToast({
                  title: result.message || "删除失败",
                  icon: "none"
                });
              }
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/admin/processes.vue:408", "删除流程失败:", error);
              let errorMessage = "删除失败，请重试";
              if (error.message && error.message !== "undefined") {
                errorMessage = error.message;
              }
              common_vendor.index.showToast({
                title: errorMessage,
                icon: "none",
                duration: 3e3
              });
            }
          }
        }
      });
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      return date.toLocaleDateString("zh-CN");
    }
  }
};
if (!Array) {
  const _component_Icon = common_vendor.resolveComponent("Icon");
  _component_Icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.handleSearch && $options.handleSearch(...args)]),
    b: $data.searchKeyword,
    c: common_vendor.p({
      name: "search",
      size: "32",
      color: "#999"
    }),
    d: $data.processList.length > 0
  }, $data.processList.length > 0 ? {
    e: common_vendor.f($options.filteredProcesses, (process, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(process.name),
        b: common_vendor.t($options.getNodeCount(process)),
        c: common_vendor.t($options.formatDate(process.created_at)),
        d: process.ticket_count !== void 0
      }, process.ticket_count !== void 0 ? {
        e: common_vendor.t(process.ticket_count)
      } : {}, {
        f: common_vendor.o(($event) => $options.designProcess(process), process.id),
        g: common_vendor.o(($event) => $options.editProcess(process), process.id),
        h: common_vendor.o(($event) => $options.deleteProcess(process), process.id),
        i: process.ticket_count > 0,
        j: process.id
      });
    })
  } : !$data.loading ? {
    g: common_vendor.o((...args) => $options.showAddModal && $options.showAddModal(...args))
  } : {}, {
    f: !$data.loading,
    h: $data.loading
  }, $data.loading ? {} : {}, {
    i: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    j: $data.refreshing,
    k: $data.processList.length > 0
  }, $data.processList.length > 0 ? {
    l: common_vendor.o((...args) => $options.showAddModal && $options.showAddModal(...args))
  } : {}, {
    m: $data.showModal
  }, $data.showModal ? common_vendor.e({
    n: common_vendor.t($data.isEdit ? "编辑流程" : "创建流程"),
    o: common_vendor.o((...args) => $options.hideModal && $options.hideModal(...args)),
    p: $data.processForm.name,
    q: common_vendor.o(($event) => $data.processForm.name = $event.detail.value),
    r: $data.processForm.description,
    s: common_vendor.o(($event) => $data.processForm.description = $event.detail.value),
    t: common_vendor.o((...args) => $options.hideModal && $options.hideModal(...args)),
    v: $data.saveLoading
  }, $data.saveLoading ? {} : {
    w: common_vendor.t($data.isEdit ? "保存" : "创建")
  }, {
    x: common_vendor.o((...args) => $options.saveProcess && $options.saveProcess(...args)),
    y: $data.saveLoading || !$data.processForm.name.trim(),
    z: common_vendor.o((...args) => $options.testClick && $options.testClick(...args)),
    A: common_vendor.o(() => {
    }),
    B: common_vendor.o((...args) => $options.hideModal && $options.hideModal(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-923b5701"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/admin/processes.js.map
