<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\Process;
use app\model\Department;
use app\model\User;
use think\Request;
use think\Response;

/**
 * 工单流程管理控制器
 */
class ProcessController extends BaseController
{
    /**
     * 获取流程列表
     */
    public function index(Request $request): Response
    {
        try {
            $page = $request->param('page', 1);
            $limit = $request->param('limit', 15);
            $search = $request->param('search', '');

            $query = Process::where('is_deleted', 0);

            // 搜索条件
            if (!empty($search)) {
                $query->whereLike('name', "%{$search}%");
            }

            $processes = $query->order('id', 'desc')
                              ->paginate([
                                  'list_rows' => $limit,
                                  'page' => $page
                              ]);

            // 格式化数据 - 修复：使用格式化后的数据
            $list = [];
            foreach ($processes->items() as $process) {
                $item = $process->toArray();
                $item['ticket_count'] = $process->tickets()->count();
                $list[] = $item;
            }

            // 修复：返回格式化后的数据，包含ticket_count字段
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $processes->total(),
                    'per_page' => $processes->listRows(),
                    'current_page' => $processes->currentPage(),
                    'last_page' => $processes->lastPage(),
                    'has_more' => $processes->hasPages()
                ],
                'timestamp' => time()
            ]);

        } catch (\Exception $e) {
            return $this->error('获取流程列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取所有流程（不分页）
     */
    public function all(): Response
    {
        try {
            $processes = Process::where('is_deleted', 0)
                               ->where('is_active', 1)
                               ->order('id', 'asc')
                               ->select();

            $list = [];
            foreach ($processes as $process) {
                $item = $process->toArray();
                $item['ticket_count'] = $process->tickets()->count();
                $list[] = $item;
            }

            return $this->success($list, '获取成功');

        } catch (\Exception $e) {
            return $this->error('获取流程列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取流程详情
     */
    public function read(Request $request): Response
    {
        try {
            $id = $request->param('id');
            
            $process = Process::find($id);
            
            if (!$process || $process->is_deleted) {
                return $this->error('流程不存在');
            }

            $data = $process->toArray();
            $data['ticket_count'] = $process->tickets()->count();

            return $this->success($data, '获取成功');

        } catch (\Exception $e) {
            return $this->error('获取流程详情失败：' . $e->getMessage());
        }
    }

    /**
     * 创建流程
     */
    public function save(Request $request): Response
    {
        try {
            $data = $request->param();
            
            if (empty($data['name'])) {
                return $this->error('流程名称不能为空');
            }

            // 创建时允许nodes为空，后续在设计页面完善
            $nodes = $data['nodes'] ?? [];
            if (!is_array($nodes)) {
                $nodes = [];
            }

            // 检查流程名称是否重复
            $existProcess = Process::where('name', $data['name'])
                                  ->where('is_deleted', 0)
                                  ->find();
            if ($existProcess) {
                return $this->error('流程名称已存在');
            }

            // 创建流程
            $process = Process::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'nodes' => $nodes,
                'is_active' => $data['is_active'] ?? true
            ]);

            // 如果有节点，验证流程配置
            if (!empty($nodes)) {
                $validation = $process->validateNodes();
                if (!$validation['valid']) {
                    // 如果验证失败，删除已创建的流程
                    $process->delete();
                    return $this->error($validation['message']);
                }
            }

            $result = $process->toArray();
            $result['ticket_count'] = 0;

            return $this->success($result, '创建成功');

        } catch (\Exception $e) {
            return $this->error('创建流程失败：' . $e->getMessage());
        }
    }

    /**
     * 更新流程
     */
    public function update(Request $request): Response
    {
        try {
            $id = $request->param('id');
            $data = $request->param();

            $process = Process::find($id);
            if (!$process || $process->is_deleted) {
                return $this->error('流程不存在');
            }

            // 检查是否有正在使用的工单
            $ticketCount = $process->tickets()
                                  ->whereIn('status', ['pending', 'processing'])
                                  ->count();
            if ($ticketCount > 0) {
                return $this->error('该流程有正在进行的工单，无法修改');
            }

            $updateData = [];

            if (isset($data['name']) && !empty($data['name'])) {
                // 检查流程名称是否重复
                $existProcess = Process::where('name', $data['name'])
                                      ->where('id', '<>', $id)
                                      ->where('is_deleted', 0)
                                      ->find();
                if ($existProcess) {
                    return $this->error('流程名称已存在');
                }
                $updateData['name'] = $data['name'];
            }

            if (isset($data['description'])) {
                $updateData['description'] = $data['description'];
            }

            if (isset($data['nodes']) && is_array($data['nodes'])) {
                $updateData['nodes'] = $data['nodes'];
            }

            if (isset($data['is_active'])) {
                $updateData['is_active'] = $data['is_active'];
            }

            if (empty($updateData)) {
                return $this->error('没有需要更新的数据');
            }

            // 更新流程
            $process->save($updateData);

            // 如果更新了节点配置，验证流程配置
            if (isset($updateData['nodes'])) {
                $validation = $process->validateNodes();
                if (!$validation['valid']) {
                    return $this->error($validation['message']);
                }
            }

            $result = $process->toArray();
            $result['ticket_count'] = $process->tickets()->count();

            return $this->success($result, '更新成功');

        } catch (\Exception $e) {
            return $this->error('更新流程失败：' . $e->getMessage());
        }
    }

    /**
     * 更新流程设计
     */
    public function updateDesign(Request $request): Response
    {
        try {
            $id = $request->param('id');
            $data = $request->param();

            $process = Process::find($id);
            if (!$process) {
                return $this->error('流程不存在');
            }

            // 构建节点数据
            $nodes = [];

            // 第一个节点必须是提交节点
            if (isset($data['submitDepartments']) && is_array($data['submitDepartments'])) {
                $nodes[] = [
                    'type' => 'submit',
                    'name' => '提交工单',
                    'departments' => $data['submitDepartments']
                ];
            } else {
                return $this->error('必须设置可提交部门');
            }

            // 添加审批节点
            if (isset($data['nodes']) && is_array($data['nodes'])) {
                foreach ($data['nodes'] as $node) {
                    // 转换前端字段名到后端期望的格式
                    $approvalNode = [
                        'type' => $node['type'] ?? 'approve',
                        'name' => $node['name'] ?? '审批节点'
                    ];

                    // 处理审批类型
                    if (isset($node['approvalType'])) {
                        $approvalNode['approve_type'] = $node['approvalType'];

                        // 处理部门审批
                        if ($node['approvalType'] === 'department' && isset($node['departments'])) {
                            // 取第一个部门的ID（简化处理）
                            if (is_array($node['departments']) && !empty($node['departments'])) {
                                $approvalNode['department_id'] = $node['departments'][0]['id'];
                            }
                        }

                        // 处理用户审批
                        if ($node['approvalType'] === 'user' && isset($node['users'])) {
                            $approvalNode['user_ids'] = array_column($node['users'], 'id');
                        }
                    }

                    $nodes[] = $approvalNode;
                }
            }

            // 至少需要一个审批节点（提交节点 + 至少一个审批节点）
            if (count($nodes) < 2) {
                return $this->error('至少需要一个审批节点');
            }

            // 更新流程
            $process->nodes = $nodes;
            $process->save();

            // 验证流程配置
            if (!empty($nodes)) {
                $validation = $process->validateNodes();
                if (!$validation['valid']) {
                    return $this->error('流程配置无效：' . $validation['message']);
                }
            }

            return $this->success($process->toArray(), '流程设计更新成功');

        } catch (\Exception $e) {
            return $this->error('更新流程设计失败：' . $e->getMessage());
        }
    }

    /**
     * 删除流程
     */
    public function delete(Request $request): Response
    {
        try {
            $id = $request->param('id');

            $process = Process::find($id);
            if (!$process || $process->is_deleted) {
                return $this->error('流程不存在');
            }

            // 检查是否有关联的工单
            $ticketCount = $process->tickets()->count();
            if ($ticketCount > 0) {
                return $this->error("该流程有{$ticketCount}个关联工单，无法删除");
            }

            // 软删除流程
            $process->is_deleted = true;
            $process->save();

            return $this->success([], '删除成功');

        } catch (\Exception $e) {
            return $this->error('删除流程失败：' . $e->getMessage());
        }
    }

    /**
     * 获取流程选项（用于下拉选择）
     */
    public function options(): Response
    {
        try {
            $options = Process::getSelectOptions();
            
            return $this->success($options, '获取成功');

        } catch (\Exception $e) {
            return $this->error('获取流程选项失败：' . $e->getMessage());
        }
    }

    /**
     * 验证流程配置
     */
    public function validateProcess(Request $request): Response
    {
        try {
            $nodes = $request->param('nodes', []);
            
            if (empty($nodes) || !is_array($nodes)) {
                return $this->error('流程节点不能为空');
            }

            // 创建临时流程对象进行验证
            $tempProcess = new Process();
            $tempProcess->nodes = $nodes;
            
            $validation = $tempProcess->validateNodes();
            
            if ($validation['valid']) {
                return $this->success([], '流程配置有效');
            } else {
                return $this->error($validation['message']);
            }

        } catch (\Exception $e) {
            return $this->error('验证流程配置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取节点审批人
     */
    public function getNodeApprovers(Request $request): Response
    {
        try {
            $processId = $request->param('process_id');
            $nodeIndex = $request->param('node_index');

            $process = Process::find($processId);
            if (!$process || $process->is_deleted) {
                return $this->error('流程不存在');
            }

            $approvers = $process->getNodeApprovers($nodeIndex);

            return $this->success($approvers, '获取成功');

        } catch (\Exception $e) {
            return $this->error('获取节点审批人失败：' . $e->getMessage());
        }
    }

    /**
     * 检查用户是否可以提交流程
     */
    public function checkUserCanSubmit(Request $request): Response
    {
        try {
            $processId = $request->param('process_id');
            $userId = $request->user_id;

            $process = Process::find($processId);
            if (!$process || $process->is_deleted) {
                return $this->error('流程不存在');
            }

            $canSubmit = $process->canUserSubmit($userId);
            $isInChain = $process->isUserInApprovalChain($userId);

            $message = '可以提交';
            if (!$canSubmit) {
                $message = $isInChain ? '您在此审批流程中，无法发布' : '您的部门不在允许提交的部门列表中';
            }

            return $this->success([
                'can_submit' => $canSubmit,
                'is_in_approval_chain' => $isInChain,
                'message' => $message
            ], '检查完成');

        } catch (\Exception $e) {
            return $this->error('检查权限失败：' . $e->getMessage());
        }
    }
}
