"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      processId: "",
      processName: "",
      processDescription: "",
      saveLoading: false,
      // 节点数据
      selectedSubmitDepartments: [],
      // 可提交部门
      approvalNodes: [],
      // 审批节点
      // 弹窗状态
      showNodeModal: false,
      showDepartmentModal: false,
      showUserModal: false,
      isEditNode: false,
      editNodeIndex: -1,
      // 表单数据
      nodeForm: {
        name: "",
        type: "department",
        // department | person
        departmentId: "",
        departmentName: "",
        approvers: [],
        allowTransfer: true,
        allowReject: true
      },
      currentApprover: "",
      selectedDepartmentIndex: -1,
      // 基础数据
      departmentList: [],
      userList: []
    };
  },
  computed: {
    selectedDepartmentName() {
      if (this.selectedDepartmentIndex >= 0) {
        return this.departmentList[this.selectedDepartmentIndex].name;
      }
      return "";
    },
    // 是否选择了全部部门
    isAllDepartmentsSelected() {
      return this.departmentList.length > 0 && this.selectedSubmitDepartments.length === this.departmentList.length;
    }
  },
  async onLoad(options) {
    this.processId = options.id;
    this.processName = decodeURIComponent(options.name || "未命名流程");
    await this.loadDepartments();
    await this.loadUsers();
    await this.loadProcessDetail();
  },
  methods: {
    // 加载部门列表
    async loadDepartments() {
      try {
        const result = await this.$api.department.list();
        common_vendor.index.__f__("log", "at pages/admin/process-design.vue:399", "部门API返回:", result);
        if (result.code === 200) {
          if (result.data && result.data.list) {
            this.departmentList = result.data.list;
          } else {
            this.departmentList = result.data || [];
          }
          common_vendor.index.__f__("log", "at pages/admin/process-design.vue:407", "部门列表:", this.departmentList);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/admin/process-design.vue:410", "加载部门列表失败:", error);
      }
    },
    // 加载用户列表
    async loadUsers() {
      try {
        const result = await this.$api.user.getList();
        common_vendor.index.__f__("log", "at pages/admin/process-design.vue:418", "用户API返回:", result);
        if (result.code === 200) {
          if (result.data && result.data.list) {
            this.userList = result.data.list;
          } else {
            this.userList = result.data || [];
          }
          common_vendor.index.__f__("log", "at pages/admin/process-design.vue:426", "用户列表:", this.userList);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/admin/process-design.vue:429", "加载用户列表失败:", error);
      }
    },
    // 加载流程详情
    async loadProcessDetail() {
      try {
        const result = await this.$api.process.getDetail(this.processId);
        common_vendor.index.__f__("log", "at pages/admin/process-design.vue:437", "流程详情API返回:", result);
        if (result.code === 200) {
          const process = result.data;
          const nodes = process.nodes || [];
          this.processDescription = process.description || "";
          if (nodes.length > 0 && nodes[0].type === "submit") {
            const submitNode = nodes[0];
            const departmentIds = submitNode.departments || [];
            this.selectedSubmitDepartments = this.departmentList.filter(
              (dept) => departmentIds.includes(dept.id)
            );
            this.approvalNodes = nodes.slice(1);
          } else {
            this.selectedSubmitDepartments = [];
            this.approvalNodes = [];
          }
          common_vendor.index.__f__("log", "at pages/admin/process-design.vue:461", "加载的提交部门:", this.selectedSubmitDepartments);
          common_vendor.index.__f__("log", "at pages/admin/process-design.vue:462", "加载的审批节点:", this.approvalNodes);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/admin/process-design.vue:465", "加载流程详情失败:", error);
      }
    },
    // 获取节点类型文本
    getNodeTypeText(type) {
      const typeMap = {
        "department": "部门审批",
        "person": "个人审批"
      };
      return typeMap[type] || "未知类型";
    },
    // 显示添加节点弹窗
    showAddNodeModal() {
      this.isEditNode = false;
      this.editNodeIndex = -1;
      this.nodeForm = {
        name: "",
        type: "department",
        departmentId: "",
        departmentName: "",
        approvers: [],
        allowTransfer: true,
        allowReject: true
      };
      this.currentApprover = "";
      this.selectedDepartmentIndex = -1;
      this.showNodeModal = true;
    },
    // 编辑节点
    editNode(index) {
      this.isEditNode = true;
      this.editNodeIndex = index;
      const node = this.approvalNodes[index];
      this.nodeForm = {
        name: node.name || "",
        type: node.type,
        departmentId: node.departmentId || "",
        departmentName: node.departmentName || "",
        approvers: [...node.approvers || []],
        allowTransfer: node.allowTransfer !== false,
        allowReject: node.allowReject !== false
      };
      if (node.departmentId) {
        this.selectedDepartmentIndex = this.departmentList.findIndex(
          (dept) => dept.id === node.departmentId
        );
      } else {
        this.selectedDepartmentIndex = -1;
      }
      this.currentApprover = "";
      this.showNodeModal = true;
    },
    // 删除节点
    deleteNode(index) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这个审批节点吗？",
        success: (res) => {
          if (res.confirm) {
            this.approvalNodes.splice(index, 1);
          }
        }
      });
    },
    // 隐藏节点弹窗
    hideNodeModal() {
      this.showNodeModal = false;
    },
    // 选择节点类型（单选，不能取消）
    selectNodeType(type) {
      this.nodeForm.type = type;
      this.nodeForm.departmentId = "";
      this.nodeForm.departmentName = "";
      this.nodeForm.approvers = [];
      this.selectedDepartmentIndex = -1;
    },
    // 节点类型变化（保留原方法以防其他地方使用）
    onNodeTypeChange(e) {
      this.nodeForm.type = e.detail.value;
      this.nodeForm.departmentId = "";
      this.nodeForm.departmentName = "";
      this.nodeForm.approvers = [];
      this.selectedDepartmentIndex = -1;
    },
    // 部门选择变化
    onDepartmentChange(e) {
      this.selectedDepartmentIndex = e.detail.value;
      const dept = this.departmentList[e.detail.value];
      this.nodeForm.departmentId = dept.id;
      this.nodeForm.departmentName = dept.name;
    },
    // 添加审批人
    addApprover() {
      const approver = this.currentApprover.trim();
      if (!approver) {
        common_vendor.index.showToast({
          title: "请输入工号",
          icon: "none"
        });
        return;
      }
      if (this.nodeForm.approvers.includes(approver)) {
        common_vendor.index.showToast({
          title: "工号已存在",
          icon: "none"
        });
        return;
      }
      this.nodeForm.approvers.push(approver);
      this.currentApprover = "";
    },
    // 检查用户是否已选择
    isUserSelected(userId) {
      return this.nodeForm.approvers.includes(userId);
    },
    // 用户选择变化
    onUserChange(e) {
      const userId = parseInt(e.detail.value);
      if (e.detail.checked) {
        if (!this.isUserSelected(userId)) {
          this.nodeForm.approvers.push(userId);
        }
      } else {
        this.nodeForm.approvers = this.nodeForm.approvers.filter((id) => id !== userId);
      }
    },
    // 获取用户显示名称
    getUserDisplayName(userId) {
      const user = this.userList.find((u) => u.id === userId);
      return user ? `${user.departmentName}-${user.name}` : `用户${userId}`;
    },
    // 移除用户
    removeUser(index) {
      this.nodeForm.approvers.splice(index, 1);
    },
    // 移除审批人（保留兼容性）
    removeApprover(index) {
      this.nodeForm.approvers.splice(index, 1);
    },
    // 允许转单变化
    onAllowTransferChange(e) {
      this.nodeForm.allowTransfer = e.detail.value.length > 0;
    },
    // 允许驳回变化
    onAllowRejectChange(e) {
      this.nodeForm.allowReject = e.detail.value.length > 0;
    },
    // 验证节点表单
    validateNodeForm() {
      if (!this.nodeForm.name.trim()) {
        return false;
      }
      if (this.nodeForm.type === "department" && !this.nodeForm.departmentId) {
        return false;
      }
      if (this.nodeForm.type === "person" && this.nodeForm.approvers.length === 0) {
        return false;
      }
      return true;
    },
    // 保存节点
    saveNode() {
      if (!this.validateNodeForm()) {
        common_vendor.index.showToast({
          title: "请完善节点信息",
          icon: "none"
        });
        return;
      }
      const nodeData = { ...this.nodeForm };
      if (this.isEditNode) {
        this.approvalNodes.splice(this.editNodeIndex, 1, nodeData);
      } else {
        this.approvalNodes.push(nodeData);
      }
      this.hideNodeModal();
    },
    // 显示提交部门选择弹窗
    showSubmitDepartmentPicker() {
      this.showDepartmentModal = true;
    },
    // 隐藏部门选择弹窗
    hideDepartmentModal() {
      this.showDepartmentModal = false;
    },
    // 显示用户选择弹窗
    showUserPicker() {
      this.showUserModal = true;
    },
    // 隐藏用户选择弹窗
    hideUserModal() {
      this.showUserModal = false;
    },
    // 切换用户选择
    toggleUser(user) {
      const userId = user.id;
      if (this.isUserSelected(userId)) {
        this.nodeForm.approvers = this.nodeForm.approvers.filter((u) => u.id !== userId);
      } else {
        this.nodeForm.approvers.push({
          id: user.id,
          name: user.name,
          departmentName: user.departmentName
        });
      }
    },
    // 检查提交部门是否已选择
    isSubmitDepartmentSelected(deptId) {
      return this.selectedSubmitDepartments.some((dept) => dept.id === deptId);
    },
    // 全部部门选择变化
    onAllDepartmentsChange(e) {
      if (e.detail.checked) {
        this.selectedSubmitDepartments = [...this.departmentList];
      } else {
        this.selectedSubmitDepartments = [];
      }
    },
    // 切换提交部门选择
    toggleSubmitDepartment(dept) {
      common_vendor.index.__f__("log", "at pages/admin/process-design.vue:731", "切换部门选择:", dept);
      if (this.isSubmitDepartmentSelected(dept.id)) {
        this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(
          (d) => d.id !== dept.id
        );
      } else {
        this.selectedSubmitDepartments.push(dept);
      }
      common_vendor.index.__f__("log", "at pages/admin/process-design.vue:743", "部门选择变化后:", this.selectedSubmitDepartments);
    },
    // 提交部门选择变化（保留兼容性）
    onSubmitDepartmentChange(e) {
      const deptId = parseInt(e.detail.value);
      const dept = this.departmentList.find((d) => d.id === deptId);
      common_vendor.index.__f__("log", "at pages/admin/process-design.vue:751", "部门选择变化:", {
        deptId,
        dept,
        checked: e.detail.checked,
        currentSelected: this.selectedSubmitDepartments
      });
      if (e.detail.checked) {
        if (!this.isSubmitDepartmentSelected(deptId)) {
          this.selectedSubmitDepartments.push(dept);
        }
      } else {
        this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(
          (d) => d.id !== deptId
        );
      }
      common_vendor.index.__f__("log", "at pages/admin/process-design.vue:768", "部门选择变化后:", this.selectedSubmitDepartments);
    },
    // 移除提交部门
    removeSubmitDepartment(deptId) {
      this.selectedSubmitDepartments = this.selectedSubmitDepartments.filter(
        (dept) => dept.id !== deptId
      );
    },
    // 保存流程
    async saveProcess() {
      common_vendor.index.__f__("log", "at pages/admin/process-design.vue:780", "保存流程 - 选中的部门:", this.selectedSubmitDepartments);
      common_vendor.index.__f__("log", "at pages/admin/process-design.vue:781", "保存流程 - 部门数量:", this.selectedSubmitDepartments.length);
      if (this.selectedSubmitDepartments.length === 0) {
        common_vendor.index.showToast({
          title: "请至少选择一个可提交部门",
          icon: "none"
        });
        return;
      }
      if (this.approvalNodes.length === 0) {
        common_vendor.index.showToast({
          title: "请至少添加一个审批节点",
          icon: "none"
        });
        return;
      }
      this.saveLoading = true;
      try {
        const formattedNodes = this.approvalNodes.map((node) => {
          const formattedNode = {
            type: "approve",
            // 统一设置为审批节点
            name: node.name || "审批节点"
          };
          if (node.type === "department") {
            formattedNode.approvalType = "department";
            formattedNode.departments = [{
              id: node.departmentId,
              name: node.departmentName
            }];
          } else if (node.type === "user") {
            formattedNode.approvalType = "user";
            formattedNode.users = node.approvers || [];
          }
          return formattedNode;
        });
        const processData = {
          submitDepartments: this.selectedSubmitDepartments.map((dept) => dept.id),
          nodes: formattedNodes
        };
        common_vendor.index.__f__("log", "at pages/admin/process-design.vue:829", "发送的流程数据:", processData);
        const result = await this.$api.process.updateDesign(this.processId, processData);
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: result.message || "保存失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/admin/process-design.vue:849", "保存流程失败:", error);
        common_vendor.index.showToast({
          title: "保存失败，请重试",
          icon: "none"
        });
      } finally {
        this.saveLoading = false;
      }
    },
    // 上移节点
    moveNodeUp(index) {
      if (index > 0) {
        const nodeToMove = this.approvalNodes.splice(index, 1)[0];
        this.approvalNodes.splice(index - 1, 0, nodeToMove);
        common_vendor.index.showToast({
          title: "节点已上移",
          icon: "success",
          duration: 1e3
        });
      }
    },
    // 下移节点
    moveNodeDown(index) {
      if (index < this.approvalNodes.length - 1) {
        const nodeToMove = this.approvalNodes.splice(index, 1)[0];
        this.approvalNodes.splice(index + 1, 0, nodeToMove);
        common_vendor.index.showToast({
          title: "节点已下移",
          icon: "success",
          duration: 1e3
        });
      }
    }
  }
};
if (!Array) {
  const _component_Icon = common_vendor.resolveComponent("Icon");
  _component_Icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.processName),
    b: $data.processDescription
  }, $data.processDescription ? {
    c: common_vendor.t($data.processDescription)
  } : {}, {
    d: $data.saveLoading
  }, $data.saveLoading ? {} : {}, {
    e: common_vendor.o((...args) => $options.saveProcess && $options.saveProcess(...args)),
    f: $data.saveLoading,
    g: common_vendor.p({
      name: "edit",
      size: "32",
      color: "#fff"
    }),
    h: common_vendor.f($data.selectedSubmitDepartments, (dept, k0, i0) => {
      return {
        a: common_vendor.t(dept.name),
        b: common_vendor.o(($event) => $options.removeSubmitDepartment(dept.id), dept.id),
        c: dept.id
      };
    }),
    i: common_vendor.o((...args) => $options.showSubmitDepartmentPicker && $options.showSubmitDepartmentPicker(...args)),
    j: common_vendor.f($data.approvalNodes, (node, index, i0) => {
      return common_vendor.e({
        a: "7074fd25-1-" + i0,
        b: common_vendor.t(node.name || `审批节点 ${index + 1}`),
        c: common_vendor.t($options.getNodeTypeText(node.type)),
        d: common_vendor.o(($event) => $options.moveNodeUp(index), index),
        e: index === 0,
        f: common_vendor.o(($event) => $options.moveNodeDown(index), index),
        g: index === $data.approvalNodes.length - 1,
        h: common_vendor.o(($event) => $options.editNode(index), index),
        i: common_vendor.o(($event) => $options.deleteNode(index), index),
        j: common_vendor.t($options.getNodeTypeText(node.type)),
        k: node.type === "department"
      }, node.type === "department" ? {
        l: common_vendor.t(node.departmentName)
      } : {}, {
        m: node.type === "person"
      }, node.type === "person" ? {
        n: common_vendor.t(node.approvers.join(", "))
      } : {}, {
        o: common_vendor.t(node.allowTransfer ? "是" : "否"),
        p: common_vendor.t(node.allowReject ? "是" : "否"),
        q: index
      });
    }),
    k: common_vendor.p({
      name: "user",
      size: "32",
      color: "#fff"
    }),
    l: common_vendor.o((...args) => $options.showAddNodeModal && $options.showAddNodeModal(...args)),
    m: common_vendor.p({
      name: "edit",
      size: "28",
      color: "#fff"
    }),
    n: common_vendor.f($data.approvalNodes, (node, index, i0) => {
      return {
        a: "7074fd25-3-" + i0,
        b: common_vendor.t(node.name || `审批节点${index + 1}`),
        c: index
      };
    }),
    o: common_vendor.p({
      name: "user",
      size: "28",
      color: "#fff"
    }),
    p: common_vendor.p({
      name: "check",
      size: "28",
      color: "#fff"
    }),
    q: $data.showNodeModal
  }, $data.showNodeModal ? common_vendor.e({
    r: common_vendor.t($data.isEditNode ? "编辑节点" : "添加节点"),
    s: common_vendor.o((...args) => $options.hideNodeModal && $options.hideNodeModal(...args)),
    t: $data.nodeForm.name,
    v: common_vendor.o(($event) => $data.nodeForm.name = $event.detail.value),
    w: $data.nodeForm.type === "department",
    x: common_vendor.o(($event) => $options.selectNodeType("department")),
    y: $data.nodeForm.type === "person",
    z: common_vendor.o(($event) => $options.selectNodeType("person")),
    A: $data.nodeForm.type === "department"
  }, $data.nodeForm.type === "department" ? common_vendor.e({
    B: $data.nodeForm.departmentId
  }, $data.nodeForm.departmentId ? {
    C: common_vendor.t($options.selectedDepartmentName)
  } : {}, {
    D: $data.departmentList,
    E: common_vendor.o((...args) => $options.onDepartmentChange && $options.onDepartmentChange(...args)),
    F: $data.selectedDepartmentIndex
  }) : {}, {
    G: $data.nodeForm.type === "person"
  }, $data.nodeForm.type === "person" ? common_vendor.e({
    H: $data.nodeForm.approvers.length > 0
  }, $data.nodeForm.approvers.length > 0 ? {
    I: common_vendor.t($data.nodeForm.approvers.length)
  } : {}, {
    J: common_vendor.o(($event) => $data.showUserModal = true),
    K: $data.nodeForm.approvers.length > 0
  }, $data.nodeForm.approvers.length > 0 ? {
    L: common_vendor.f($data.nodeForm.approvers, (userId, index, i0) => {
      return {
        a: common_vendor.t($options.getUserDisplayName(userId)),
        b: common_vendor.o(($event) => $options.removeUser(index), index),
        c: index
      };
    })
  } : {}) : {}, {
    M: $data.nodeForm.allowTransfer,
    N: common_vendor.o((...args) => $options.onAllowTransferChange && $options.onAllowTransferChange(...args)),
    O: $data.nodeForm.allowReject,
    P: common_vendor.o((...args) => $options.onAllowRejectChange && $options.onAllowRejectChange(...args)),
    Q: common_vendor.o((...args) => $options.hideNodeModal && $options.hideNodeModal(...args)),
    R: common_vendor.t($data.isEditNode ? "保存" : "添加"),
    S: common_vendor.o((...args) => $options.saveNode && $options.saveNode(...args)),
    T: !$options.validateNodeForm(),
    U: common_vendor.o(() => {
    }),
    V: common_vendor.o((...args) => $options.hideNodeModal && $options.hideNodeModal(...args))
  }) : {}, {
    W: $data.showDepartmentModal
  }, $data.showDepartmentModal ? {
    X: common_vendor.o((...args) => $options.hideDepartmentModal && $options.hideDepartmentModal(...args)),
    Y: $options.isAllDepartmentsSelected,
    Z: common_vendor.o((...args) => $options.onAllDepartmentsChange && $options.onAllDepartmentsChange(...args)),
    aa: common_vendor.f($data.departmentList, (dept, k0, i0) => {
      return {
        a: String(dept.id),
        b: $options.isSubmitDepartmentSelected(dept.id),
        c: common_vendor.t(dept.name),
        d: dept.id,
        e: common_vendor.o(($event) => $options.toggleSubmitDepartment(dept), dept.id)
      };
    }),
    ab: common_vendor.o((...args) => $options.hideDepartmentModal && $options.hideDepartmentModal(...args)),
    ac: common_vendor.o(() => {
    }),
    ad: common_vendor.o((...args) => $options.hideDepartmentModal && $options.hideDepartmentModal(...args))
  } : {}, {
    ae: $data.showUserModal
  }, $data.showUserModal ? {
    af: common_vendor.o((...args) => $options.hideUserModal && $options.hideUserModal(...args)),
    ag: common_vendor.f($data.userList, (user, k0, i0) => {
      return {
        a: String(user.id),
        b: $options.isUserSelected(user.id),
        c: common_vendor.t(user.departmentName),
        d: common_vendor.t(user.name),
        e: user.id,
        f: common_vendor.o(($event) => $options.toggleUser(user), user.id)
      };
    }),
    ah: common_vendor.o((...args) => $options.hideUserModal && $options.hideUserModal(...args)),
    ai: common_vendor.o(() => {
    }),
    aj: common_vendor.o((...args) => $options.hideUserModal && $options.hideUserModal(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7074fd25"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/admin/process-design.js.map
