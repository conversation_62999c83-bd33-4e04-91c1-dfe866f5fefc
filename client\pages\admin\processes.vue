<template>
  <view class="processes-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-wrapper">
        <input 
          class="search-input" 
          type="text" 
          placeholder="搜索流程名称..."
          v-model="searchKeyword"
          @input="handleSearch"
        />
        <Icon name="search" size="32" color="#999" />
      </view>
    </view>

    <!-- 流程列表 -->
    <view class="list-section">
      <scroll-view 
        scroll-y 
        class="scroll-view"
        refresher-enabled
        @refresherrefresh="onRefresh"
        :refresher-triggered="refreshing"
      >
        <view v-if="processList.length > 0">
          <view 
            v-for="process in filteredProcesses" 
            :key="process.id"
            class="process-card"
          >
            <view class="process-info">
              <view class="process-details">
                <text class="process-name">{{ process.name }}</text>
                <text class="process-meta">
                  节点数量：{{ getNodeCount(process) }}个
                </text>
                <text class="process-meta">
                  创建时间：{{ formatDate(process.created_at) }}
                </text>
                <text class="process-meta" v-if="process.ticket_count !== undefined">
                  使用次数：{{ process.ticket_count }}次
                </text>
              </view>
            </view>
            
            <view class="process-actions">
              <button class="action-btn design-btn" @click="designProcess(process)">
                设计
              </button>
              <button class="action-btn edit-btn" @click="editProcess(process)">
                编辑
              </button>
              <button
                class="action-btn delete-btn"
                @click="deleteProcess(process)"
                :disabled="process.ticket_count > 0"
              >
                删除
              </button>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else-if="!loading" class="empty-state">
          <text class="empty-text">暂无流程数据</text>
          <button class="add-first-btn" @click="showAddModal">创建第一个流程</button>
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <text>加载中...</text>
        </view>
      </scroll-view>
    </view>

    <!-- 浮动添加按钮 -->
    <view class="floating-btn" @click="showAddModal" v-if="processList.length > 0">
      <text class="btn-icon">+</text>
    </view>

    <!-- 添加/编辑流程弹窗 -->
    <view class="modal-overlay" v-if="showModal" @click="hideModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ isEdit ? '编辑流程' : '创建流程' }}</text>
          <text class="modal-close" @click="hideModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <view class="form-label">流程名称</view>
            <input 
              class="form-input" 
              type="text" 
              placeholder="请输入流程名称"
              v-model="processForm.name"
              maxlength="50"
            />
          </view>
          
          <view class="form-item">
            <view class="form-label">流程描述</view>
            <textarea 
              class="form-textarea" 
              placeholder="请输入流程描述（可选）"
              v-model="processForm.description"
              maxlength="200"
            />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @click="hideModal">取消</button>
          <button
            class="modal-btn confirm-btn"
            @click="saveProcess"
            :disabled="saveLoading || !processForm.name.trim()"
          >
            <text v-if="saveLoading">保存中...</text>
            <text v-else>{{ isEdit ? '保存' : '创建' }}</text>
          </button>
          <!-- 临时测试按钮 -->
          <button class="modal-btn" @click="testClick" style="background: orange; color: white;">测试</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      refreshing: false,
      saveLoading: false,
      searchKeyword: '',
      showModal: false,
      isEdit: false,
      
      processList: [],
      
      processForm: {
        id: '',
        name: '',
        description: ''
      }
    }
  },
  
  computed: {
    // 过滤后的流程列表
    filteredProcesses() {
      if (!this.searchKeyword.trim()) {
        return this.processList
      }
      
      return this.processList.filter(process => 
        process.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  
  onLoad() {
    console.log('页面加载完成')
    console.log('API对象:', this.$api)
    console.log('process API:', this.$api.process)
    this.loadProcesses()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.loadProcesses()
  },
  
  methods: {
    // 获取节点数量
    getNodeCount(process) {
      if (process.nodes && Array.isArray(process.nodes)) {
        return process.nodes.length
      }
      return 0
    },

    // 加载流程列表
    async loadProcesses() {
      this.loading = true

      try {
        const result = await this.$api.process.list()
        console.log('流程API返回:', result)
        if (result.code === 200) {
          // 统一数据格式，确保包含所有必要字段
          let processList = []
          if (result.data && result.data.list) {
            processList = result.data.list
          } else if (Array.isArray(result.data)) {
            processList = result.data
          } else {
            processList = []
          }

          // 确保每个流程都有必要的字段
          this.processList = processList.map(process => ({
            ...process,
            ticket_count: process.ticket_count || 0,
            nodes: process.nodes || [],
            description: process.description || ''
          }))

          console.log('格式化后的流程列表:', this.processList)
        } else {
          console.error('API返回错误:', result)
          uni.showToast({
            title: result.message || '加载失败',
            icon: 'none',
            duration: 3000
          })
        }
      } catch (error) {
        console.error('加载流程列表失败:', error)
        let errorMessage = '加载失败，请重试'
        if (error.message && error.message !== 'undefined') {
          errorMessage = error.message
        }
        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 搜索处理
    handleSearch() {
      // 实时搜索，无需额外处理
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadProcesses()
    },
    
    // 显示添加弹窗
    showAddModal() {
      this.isEdit = false
      this.processForm = {
        id: '',
        name: '',
        description: ''
      }
      this.showModal = true
    },
    
    // 编辑流程基本信息
    editProcess(process) {
      this.isEdit = true
      this.processForm = {
        id: process.id,
        name: process.name,
        description: process.description || ''
      }
      this.showModal = true
    },
    
    // 设计流程
    designProcess(process) {
      uni.navigateTo({
        url: `/pages/admin/process-design?id=${process.id}&name=${encodeURIComponent(process.name)}`
      })
    },
    
    // 隐藏弹窗
    hideModal() {
      this.showModal = false
      this.processForm = {
        id: '',
        name: '',
        description: ''
      }
    },
    
    // 测试方法
    testClick() {
      console.log('测试点击事件正常工作')
      uni.showToast({
        title: '点击事件正常',
        icon: 'success'
      })
    },

    // 保存流程
    async saveProcess() {
      console.log('saveProcess 方法被调用')
      console.log('表单数据:', this.processForm)
      console.log('是否编辑模式:', this.isEdit)

      if (!this.processForm.name.trim()) {
        uni.showToast({
          title: '请输入流程名称',
          icon: 'none'
        })
        return
      }

      this.saveLoading = true
      console.log('开始保存，saveLoading:', this.saveLoading)

      try {
        let result
        const requestData = {
          name: this.processForm.name.trim(),
          description: this.processForm.description.trim()
        }
        console.log('请求数据:', requestData)

        if (this.isEdit) {
          console.log('执行更新操作，ID:', this.processForm.id)
          result = await this.$api.process.update(this.processForm.id, requestData)
        } else {
          console.log('执行创建操作')
          result = await this.$api.process.create(requestData)
        }

        console.log('API返回结果:', result)

        if (result.code === 200) {
          uni.showToast({
            title: this.isEdit ? '修改成功' : '创建成功',
            icon: 'success'
          })

          this.hideModal()
          this.loadProcesses()

          // 如果是新创建的流程，直接跳转到设计页面
          if (!this.isEdit && result.data && result.data.id) {
            setTimeout(() => {
              uni.navigateTo({
                url: `/pages/admin/process-design?id=${result.data.id}&name=${encodeURIComponent(result.data.name)}`
              })
            }, 1500)
          }
        } else {
          console.error('API返回错误:', result)
          uni.showToast({
            title: result.message || '操作失败',
            icon: 'none',
            duration: 3000
          })
        }
      } catch (error) {
        console.error('保存流程失败:', error)
        let errorMessage = '操作失败，请重试'
        if (error.message && error.message !== 'undefined') {
          errorMessage = error.message
        }
        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.saveLoading = false
        console.log('保存完成，saveLoading:', this.saveLoading)
      }
    },
    
    // 删除流程
    deleteProcess(process) {
      if (process.ticket_count > 0) {
        uni.showToast({
          title: `该流程已被使用${process.ticket_count}次，无法删除`,
          icon: 'none'
        })
        return
      }
      
      uni.showModal({
        title: '确认删除',
        content: `确定要删除流程"${process.name}"吗？删除后无法恢复。`,
        confirmText: '删除',
        confirmColor: '#e74c3c',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await this.$api.process.delete(process.id)
              
              if (result.code === 200) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                this.loadProcesses()
              } else {
                uni.showToast({
                  title: result.message || '删除失败',
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('删除流程失败:', error)
              let errorMessage = '删除失败，请重试'
              if (error.message && error.message !== 'undefined') {
                errorMessage = error.message
              }
              uni.showToast({
                title: errorMessage,
                icon: 'none',
                duration: 3000
              })
            }
          }
        }
      })
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.processes-container {
  min-height: 100vh;
  background: $bg-color-page;
  padding-bottom: 120rpx;
}

.search-section {
  background: $bg-color-white;
  padding: $spacing-lg;
  margin-bottom: $spacing-base;
  box-shadow: $box-shadow-light;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid $border-color;
  border-radius: 40rpx;
  padding: 0 $spacing-lg 0 $spacing-base;
  font-size: $font-size-base;
  background: $bg-color-white;
  
  &:focus {
    border-color: $primary-color;
  }
}

.search-icon {
  position: absolute;
  right: $spacing-base;
  font-size: $font-size-lg;
  color: $text-color-secondary;
}

.list-section {
  flex: 1;
}

.scroll-view {
  height: calc(100vh - 200rpx);
}

.process-card {
  background: $bg-color-white;
  margin: 0 $spacing-lg $spacing-base;
  padding: $spacing-lg;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-light;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.process-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.process-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: $border-radius-base;
  background: linear-gradient(135deg, $warning-color, #ff9f43);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-lg;
  margin-right: $spacing-base;
}

.process-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.process-name {
  font-size: $font-size-lg;
  font-weight: bold;
  color: $text-color-primary;
  margin-bottom: $spacing-xs;
}

.process-meta {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-bottom: 4rpx;
}

.process-actions {
  display: flex;
  gap: $spacing-sm;
}

.action-btn {
  padding: $spacing-sm $spacing-base;
  border: none;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  
  &.design-btn {
    background: $primary-color;
    color: $text-color-white;
  }
  
  &.edit-btn {
    background: $warning-color;
    color: $text-color-white;
  }
  
  &.delete-btn {
    background: $danger-color;
    color: $text-color-white;
    
    &:disabled {
      opacity: 0.5;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx $spacing-lg;
}

.empty-text {
  display: block;
  font-size: $font-size-base;
  color: $text-color-secondary;
  margin-bottom: $spacing-lg;
}

.add-first-btn {
  background: $primary-color;
  color: $text-color-white;
  border: none;
  border-radius: $border-radius-base;
  padding: $spacing-base $spacing-lg;
  font-size: $font-size-base;
}

.loading-state {
  text-align: center;
  padding: $spacing-lg;
  color: $text-color-secondary;
  font-size: $font-size-sm;
}

.floating-btn {
  position: fixed;
  bottom: 160rpx;
  right: $spacing-lg;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: $primary-color;
  color: $text-color-white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 40rpx rgba(52, 152, 219, 0.3);
  z-index: 100;
}

.btn-icon {
  font-size: 60rpx;
  font-weight: 300;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: $bg-color-white;
  border-radius: $border-radius-lg;
  width: 600rpx;
  max-width: 90vw;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-lg;
  border-bottom: 1rpx solid $border-color-light;
}

.modal-title {
  font-size: $font-size-lg;
  font-weight: bold;
  color: $text-color-primary;
}

.modal-close {
  font-size: $font-size-xxl;
  color: $text-color-secondary;
  line-height: 1;
}

.modal-body {
  padding: $spacing-lg;
}

.form-item {
  margin-bottom: $spacing-lg;
}

.form-label {
  font-size: $font-size-base;
  color: $text-color-primary;
  margin-bottom: $spacing-sm;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid $border-color;
  border-radius: $border-radius-base;
  padding: 0 $spacing-base;
  font-size: $font-size-base;
  background: $bg-color-white;
  
  &:focus {
    border-color: $primary-color;
  }
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid $border-color;
  border-radius: $border-radius-base;
  padding: $spacing-base;
  font-size: $font-size-base;
  background: $bg-color-white;
  line-height: 1.5;
  
  &:focus {
    border-color: $primary-color;
  }
}

.modal-footer {
  display: flex;
  gap: $spacing-base;
  padding: $spacing-lg;
  border-top: 1rpx solid $border-color-light;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  
  &.cancel-btn {
    background: $bg-color-light;
    color: $text-color-secondary;
  }
  
  &.confirm-btn {
    background: $primary-color;
    color: $text-color-white;
    
    &:disabled {
      opacity: 0.5;
    }
  }
}
</style>
